<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blank Test Fixer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .container {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        .file-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #fff;
        }
        .btn {
            background-color: #4361ee;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background-color: #3a56d4;
        }
        .btn:disabled {
            background-color: #a0aec0;
            cursor: not-allowed;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background-color: #f1f1f1;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Blank Test Fixer</h1>
        <p>This tool specifically fixes JSON files by clearing suggestions for test names containing "Blank".</p>
        
        <div class="form-group">
            <label for="jsonFile">Upload JSON File:</label>
            <input type="file" id="jsonFile" class="file-input" accept=".json">
        </div>
        
        <button id="fixButton" class="btn" disabled>Fix Blank Tests</button>
        
        <div id="status" style="display: none;"></div>
        
        <div id="resultContainer" style="display: none; margin-top: 20px;">
            <h3>Fixed JSON:</h3>
            <pre id="resultJson"></pre>
            <button id="downloadButton" class="btn">Download Fixed JSON</button>
        </div>
    </div>

    <script>
        // DOM elements
        const jsonFileInput = document.getElementById('jsonFile');
        const fixButton = document.getElementById('fixButton');
        const statusDiv = document.getElementById('status');
        const resultContainer = document.getElementById('resultContainer');
        const resultJson = document.getElementById('resultJson');
        const downloadButton = document.getElementById('downloadButton');
        
        // Global variables
        let jsonData = null;
        let jsonFileName = '';
        
        // Event listeners
        jsonFileInput.addEventListener('change', handleJsonFileSelect);
        fixButton.addEventListener('click', fixBlankTests);
        downloadButton.addEventListener('click', downloadFixedJson);
        
        // Handle JSON file selection
        function handleJsonFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            jsonFileName = file.name;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    jsonData = JSON.parse(e.target.result);
                    showStatus('JSON file loaded successfully.', 'success');
                    fixButton.disabled = false;
                } catch (error) {
                    showStatus(`Error parsing JSON file: ${error.message}`, 'error');
                    fixButton.disabled = true;
                }
            };
            reader.readAsText(file);
        }
        
        // Fix blank tests
        function fixBlankTests() {
            if (!jsonData) {
                showStatus('No JSON data to fix.', 'error');
                return;
            }
            
            // Create a deep copy of the data
            const fixedData = JSON.parse(JSON.stringify(jsonData));
            
            // Count how many tests were fixed
            let fixedCount = 0;
            
            // Process each test
            if (fixedData.summary_items && Array.isArray(fixedData.summary_items)) {
                fixedData.summary_items.forEach(item => {
                    if (item.testName && item.testName.includes("Blank")) {
                        console.log(`Clearing suggestions for test: ${item.testName}`);
                        item.suggestions = [];
                        fixedCount++;
                    }
                });
                
                // Show result
                if (fixedCount > 0) {
                    showStatus(`Successfully cleared suggestions for ${fixedCount} tests with "Blank" in their name.`, 'success');
                    
                    // Display the fixed JSON
                    resultJson.textContent = JSON.stringify(fixedData, null, 2);
                    resultContainer.style.display = 'block';
                } else {
                    showStatus('No tests with "Blank" in their name were found.', 'info');
                }
                
                // Store the fixed data
                jsonData = fixedData;
            } else {
                showStatus('Invalid JSON structure. Expected "summary_items" array.', 'error');
            }
        }
        
        // Download fixed JSON
        function downloadFixedJson() {
            if (!jsonData) {
                showStatus('No fixed JSON data to download.', 'error');
                return;
            }
            
            const jsonString = JSON.stringify(jsonData, null, 2);
            const blob = new Blob([jsonString], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `fixed_${jsonFileName}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            showStatus('Fixed JSON file downloaded successfully.', 'success');
        }
        
        // Show status message
        function showStatus(message, type = 'info') {
            statusDiv.className = `status status-${type}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
        }
    </script>
</body>
</html>
