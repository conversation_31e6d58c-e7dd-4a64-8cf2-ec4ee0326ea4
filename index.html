<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON Content Updater</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        :root {
            /* Primary colors */
            --primary: #4361ee;
            --primary-light: #4895ef;
            --primary-dark: #3f37c9;

            /* Secondary colors */
            --secondary: #4cc9f0;
            --accent: #f72585;

            /* Neutral colors */
            --bg-light: #f8f9fa;
            --bg-white: #ffffff;
            --text-dark: #212529;
            --text-muted: #6c757d;
            --border-color: #dee2e6;

            /* Status colors */
            --success: #4ade80;
            --warning: #fbbf24;
            --danger: #f43f5e;
            --info: #60a5fa;

            /* Spacing */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;

            /* Shadows */
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

            /* Border radius */
            --radius-sm: 0.25rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-full: 9999px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            background-color: var(--bg-light);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .app-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--space-md);
        }

        header {
            text-align: center;
            margin-bottom: var(--space-xl);
            padding: var(--space-lg) 0;
        }

        h1 {
            font-weight: 700;
            font-size: 2.5rem;
            margin-bottom: var(--space-sm);
            color: var(--primary-dark);
            letter-spacing: -0.025em;
        }

        .subtitle {
            color: var(--text-muted);
            font-size: 1.1rem;
            font-weight: 400;
        }

        /* Progress indicator */
        .progress-container {
            margin: var(--space-xl) 0;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            position: relative;
            margin-bottom: var(--space-xl);
            padding: 0 20px; /* Add padding to ensure circles don't overflow */
        }

        .progress-steps::before {
            content: '';
            position: absolute;
            top: 20px; /* Align with center of circles */
            left: 40px; /* Start after the first circle's center */
            width: calc(100% - 80px); /* Adjust width to account for padding */
            height: 2px;
            background-color: var(--border-color);
            z-index: 1;
        }

        .progress-step {
            position: relative;
            z-index: 2;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            background-color: var(--bg-white);
            border: 2px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: var(--text-muted);
            margin-bottom: var(--space-sm);
            transition: all 0.3s ease;
            position: relative; /* Ensure proper z-index */
        }

        .progress-step.active .step-circle {
            background-color: var(--primary);
            border-color: var(--primary);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .progress-step.completed .step-circle {
            background-color: var(--success);
            border-color: var(--success);
            color: white;
        }

        .progress-step.completed .step-circle::after {
            content: '✓';
            font-size: 1.2rem;
        }

        .step-label {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-muted);
            text-align: center;
            transition: all 0.3s ease;
            width: 100px; /* Fixed width for better alignment */
            margin-left: -30px; /* Center under the circle */
        }

        .progress-step.active .step-label {
            color: var(--primary);
            font-weight: 600;
        }

        .progress-step.completed .step-label {
            color: var(--success);
        }

        /* Step panels */
        .step-panel {
            display: none;
            background-color: var(--bg-white);
            border-radius: var(--radius-lg);
            padding: var(--space-xl);
            margin-bottom: var(--space-lg);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .step-panel.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .step-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: var(--space-md);
            color: var(--primary-dark);
            display: flex;
            align-items: center;
        }

        .step-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background-color: var(--primary);
            color: white;
            border-radius: var(--radius-full);
            margin-right: var(--space-sm);
            font-size: 1rem;
            font-weight: 600;
        }

        .step-description {
            color: var(--text-muted);
            margin-bottom: var(--space-lg);
        }

        /* Form elements */
        .form-group {
            margin-bottom: var(--space-lg);
        }

        .form-group label {
            display: block;
            margin-bottom: var(--space-sm);
            font-weight: 500;
            color: var(--text-dark);
        }

        .file-input-container {
            position: relative;
            width: 100%;
        }

        .file-input {
            position: absolute;
            left: 0;
            top: 0;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
            z-index: 2;
        }

        .file-input-label {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-lg);
            background-color: var(--bg-light);
            border: 2px dashed var(--border-color);
            border-radius: var(--radius-md);
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-input-label:hover {
            border-color: var(--primary);
            background-color: rgba(67, 97, 238, 0.05);
        }

        .file-input-label .material-icons-round {
            margin-right: var(--space-sm);
            font-size: 1.5rem;
            color: var(--primary);
        }

        .file-name {
            margin-top: var(--space-sm);
            font-size: 0.9rem;
            color: var(--text-muted);
        }

        .file-name.active {
            color: var(--primary);
            font-weight: 500;
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-sm) var(--space-lg);
            border-radius: var(--radius-md);
            font-weight: 500;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            outline: none;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            box-shadow: var(--shadow-md);
        }

        .btn-primary:disabled {
            background-color: var(--border-color);
            cursor: not-allowed;
            box-shadow: none;
        }

        .btn-secondary {
            background-color: var(--bg-white);
            color: var(--primary);
            border: 1px solid var(--primary);
        }

        .btn-secondary:hover {
            background-color: rgba(67, 97, 238, 0.05);
        }

        .btn-secondary.active {
            background-color: var(--primary);
            color: white;
        }

        .btn-success {
            background-color: var(--success);
            color: white;
        }

        .btn-success:hover {
            background-color: #3bca70;
            box-shadow: var(--shadow-md);
        }

        .btn .material-icons-round {
            margin-right: var(--space-xs);
            font-size: 1.2rem;
        }

        .btn-container {
            display: flex;
            justify-content: space-between;
            margin-top: var(--space-lg);
        }

        /* Status messages */
        .status {
            padding: var(--space-md);
            margin: var(--space-md) 0;
            border-radius: var(--radius-md);
            display: flex;
            align-items: flex-start;
        }

        .status .material-icons-round {
            margin-right: var(--space-sm);
            font-size: 1.2rem;
        }

        .status-success {
            background-color: rgba(74, 222, 128, 0.1);
            border-left: 4px solid var(--success);
            color: #166534;
        }

        .status-error {
            background-color: rgba(244, 63, 94, 0.1);
            border-left: 4px solid var(--danger);
            color: #9f1239;
        }

        .status-info {
            background-color: rgba(96, 165, 250, 0.1);
            border-left: 4px solid var(--info);
            color: #1e40af;
        }

        .status-warning {
            background-color: rgba(251, 191, 36, 0.1);
            border-left: 4px solid var(--warning);
            color: #92400e;
        }

        /* Section titles */
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-md);
            padding-bottom: var(--space-xs);
            border-bottom: 1px solid var(--border-color);
        }

        /* Form description */
        .form-description {
            color: var(--text-muted);
            margin-bottom: var(--space-md);
            font-size: 0.95rem;
        }

        /* Mapping options */
        .mapping-options {
            margin-bottom: var(--space-lg);
        }

        .template-options {
            display: flex;
            align-items: center;
            gap: var(--space-md);
            margin-top: var(--space-md);
        }

        .template-format {
            padding: var(--space-sm) var(--space-md);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            background-color: var(--bg-white);
            color: var(--text-dark);
            font-size: 0.9rem;
            cursor: pointer;
        }

        .template-format:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
        }

        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-dark);
            margin: 0;
        }

        /* Modal styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .modal {
            background-color: var(--bg-white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            transform: translateY(20px);
            transition: transform 0.3s ease;
        }

        .modal-overlay.active .modal {
            transform: translateY(0);
        }

        .modal-header {
            padding: var(--space-lg);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--primary-dark);
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            cursor: pointer;
            color: var(--text-muted);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-xs);
            border-radius: var(--radius-full);
            transition: all 0.2s ease;
        }

        .modal-close:hover {
            background-color: var(--bg-light);
            color: var(--danger);
        }

        .modal-body {
            padding: var(--space-lg);
            overflow-y: auto;
            max-height: calc(90vh - 140px);
        }

        .modal-footer {
            padding: var(--space-md) var(--space-lg);
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: var(--space-md);
        }

        /* Checkbox and Radio styles */
        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: var(--space-md);
            margin-bottom: var(--space-md);
        }

        .checkbox-label, .radio-label {
            display: inline-flex;
            align-items: center;
            cursor: pointer;
            user-select: none;
        }

        .checkbox-label input[type="checkbox"],
        .radio-label input[type="radio"] {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
        }

        .checkbox-label span, .radio-label span {
            position: relative;
            padding-left: 28px;
            font-weight: 500;
        }

        .checkbox-label span:before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 18px;
            height: 18px;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-sm);
            background-color: var(--bg-white);
            transition: all 0.2s ease;
        }

        .radio-label span:before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 18px;
            height: 18px;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            background-color: var(--bg-white);
            transition: all 0.2s ease;
        }

        .checkbox-label input:checked ~ span:before {
            background-color: var(--primary);
            border-color: var(--primary);
        }

        .radio-label input:checked ~ span:before {
            border-color: var(--primary);
        }

        .checkbox-label span:after {
            content: '';
            position: absolute;
            left: 6px;
            top: 3px;
            width: 6px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
            opacity: 0;
            transition: all 0.2s ease;
        }

        .radio-label span:after {
            content: '';
            position: absolute;
            left: 5px;
            top: 5px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--primary);
            opacity: 0;
            transition: all 0.2s ease;
        }

        .checkbox-label input:checked ~ span:after,
        .radio-label input:checked ~ span:after {
            opacity: 1;
        }

        /* Loading spinner */
        .loading-fields {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            color: var(--text-muted);
            font-style: italic;
        }

        .loading-spinner {
            animation: spin 1.5s linear infinite;
            color: var(--primary);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Tab styles */
        .tabs {
            margin: var(--space-lg) 0;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            overflow: hidden;
            background-color: var(--bg-white);
            box-shadow: var(--shadow-sm);
        }

        .tab-header {
            display: flex;
            background-color: var(--bg-light);
            border-bottom: 1px solid var(--border-color);
        }

        .tab-button {
            padding: var(--space-md) var(--space-lg);
            cursor: pointer;
            font-weight: 500;
            display: flex;
            align-items: center;
            color: var(--text-muted);
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .tab-button .material-icons-round {
            margin-right: var(--space-xs);
            font-size: 1.2rem;
        }

        .tab-button.active {
            color: var(--primary);
            border-bottom-color: var(--primary);
            background-color: var(--bg-white);
        }

        .badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 20px;
            height: 20px;
            padding: 0 var(--space-xs);
            border-radius: var(--radius-full);
            background-color: var(--border-color);
            color: var(--text-muted);
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: var(--space-xs);
        }

        .tab-button.active .badge {
            background-color: var(--primary);
            color: white;
        }

        .tab-content {
            display: none;
            padding: var(--space-lg);
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        /* Search and filter */
        .search-filter {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-md);
            flex-wrap: wrap;
            gap: var(--space-md);
        }

        .search-container {
            position: relative;
            flex: 1;
            min-width: 250px;
            display: flex;
            align-items: center;
        }

        .search-container .material-icons-round {
            position: absolute;
            left: var(--space-sm);
            color: var(--text-muted);
            font-size: 1.2rem;
        }

        .search-container input {
            width: 100%;
            padding: var(--space-sm) var(--space-sm) var(--space-sm) 2.5rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 1rem;
            transition: all 0.2s ease;
        }

        .search-container input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
        }

        .clear-button {
            position: absolute;
            right: var(--space-sm);
            background: none;
            border: none;
            color: var(--text-muted);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
        }

        .clear-button:hover {
            color: var(--danger);
        }

        /* Table styles */
        .table-container {
            overflow-x: auto;
            margin-bottom: var(--space-lg);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            max-height: 400px;
            overflow-y: auto;
            box-shadow: var(--shadow-sm);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }

        th, td {
            padding: var(--space-md);
            text-align: left;
            border-bottom: 1px solid var(--border-color);
            word-break: break-word;
        }

        th {
            background-color: var(--bg-light);
            font-weight: 600;
            color: var(--text-dark);
            position: sticky;
            top: 0;
            z-index: 10;
            box-shadow: 0 1px 0 var(--border-color);
        }

        /* Column widths */
        th:nth-child(1), td:nth-child(1) {
            width: 40px;
        }

        th:nth-child(2), td:nth-child(2) {
            width: 30%;
        }

        th:nth-child(3), td:nth-child(3) {
            width: 30%;
        }

        th:nth-child(4), td:nth-child(4) {
            width: 40%;
        }

        /* Select element styling */
        select.csv-test-select {
            width: 100%;
            padding: var(--space-sm);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            background-color: var(--bg-white);
            font-size: 0.9rem;
            color: var(--text-dark);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        select.csv-test-select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
        }

        tr:hover {
            background-color: rgba(67, 97, 238, 0.05);
        }

        tr.special-test {
            background-color: rgba(251, 191, 36, 0.1);
        }

        tr.special-test:hover {
            background-color: rgba(251, 191, 36, 0.2);
        }

        .checkbox-cell {
            width: 40px;
            text-align: center;
        }

        /* Preview content */
        .preview-content {
            max-height: 80px;
            overflow-y: auto;
            font-size: 0.9rem;
            background-color: var(--bg-light);
            padding: var(--space-sm);
            border-radius: var(--radius-sm);
            white-space: pre-line;
        }

        /* Action panel */
        .action-panel {
            margin: var(--space-lg) 0;
            padding: var(--space-lg);
            background-color: var(--bg-light);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
        }

        .download-options {
            display: flex;
            justify-content: center;
            margin-bottom: var(--space-lg);
            flex-wrap: wrap;
            gap: var(--space-md);
        }

        /* Progress bar */
        .progress-bar-container {
            margin: var(--space-lg) 0;
            text-align: center;
            background-color: var(--bg-white);
            padding: var(--space-lg);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
            animation: fadeIn 0.5s ease;
            border: 1px solid var(--border-color);
        }

        .progress-bar-container h3 {
            margin-bottom: var(--space-md);
            color: var(--primary-dark);
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .progress-bar-container h3::before {
            content: '';
            display: inline-block;
            width: 18px;
            height: 18px;
            border: 2px solid var(--primary);
            border-top-color: transparent;
            border-radius: 50%;
            margin-right: var(--space-sm);
            animation: spin 1s linear infinite;
        }

        .progress-bar {
            height: 10px;
            background-color: var(--border-color);
            border-radius: var(--radius-full);
            margin: var(--space-md) 0;
            overflow: hidden;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .progress-bar-fill {
            height: 100%;
            background-color: var(--primary);
            width: 0%;
            transition: width 0.5s ease;
            background-image: linear-gradient(45deg,
                rgba(255, 255, 255, 0.15) 25%,
                transparent 25%,
                transparent 50%,
                rgba(255, 255, 255, 0.15) 50%,
                rgba(255, 255, 255, 0.15) 75%,
                transparent 75%,
                transparent);
            background-size: 1rem 1rem;
            animation: progress-bar-stripes 1s linear infinite;
            position: absolute;
            top: 0;
            left: 0;
        }

        #progressText {
            margin-top: var(--space-sm);
            font-weight: 500;
            color: var(--primary-dark);
        }

        @keyframes progress-bar-stripes {
            from { background-position: 1rem 0; }
            to { background-position: 0 0; }
        }

        /* Update complete */
        .update-complete {
            margin: var(--space-lg) 0;
            animation: fadeIn 0.5s ease;
        }

        .summary-container {
            margin-top: var(--space-lg);
            padding: var(--space-lg);
            background-color: var(--bg-white);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
        }

        .summary-container h3 {
            margin-bottom: var(--space-md);
            font-weight: 600;
            color: var(--primary-dark);
            display: flex;
            align-items: center;
        }

        .summary-container h3::before {
            content: '';
            display: inline-block;
            width: 4px;
            height: 18px;
            background-color: var(--primary);
            margin-right: var(--space-sm);
            border-radius: var(--radius-sm);
        }

        .summary-container ul {
            padding-left: var(--space-lg);
        }

        .summary-container li {
            margin-bottom: var(--space-sm);
            position: relative;
        }

        .summary-container li::before {
            content: '✓';
            color: var(--success);
            position: absolute;
            left: -20px;
            font-weight: bold;
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .progress-steps {
                flex-direction: column;
                align-items: flex-start;
                margin-left: var(--space-lg);
                padding: 0;
            }

            .progress-steps::before {
                top: 20px;
                left: 20px;
                width: 2px;
                height: calc(100% - 40px);
                transform: none;
            }

            .progress-step {
                flex-direction: row;
                margin-bottom: var(--space-md);
                width: 100%;
            }

            .step-circle {
                margin-right: var(--space-md);
                margin-bottom: 0;
            }

            .step-label {
                text-align: left;
                margin-left: 0;
                width: auto;
            }

            .checkbox-group {
                flex-direction: column;
                gap: var(--space-sm);
            }

            .search-filter {
                flex-direction: column;
                align-items: stretch;
            }

            .btn-container {
                flex-direction: column;
                gap: var(--space-md);
            }

            .btn-container button {
                width: 100%;
            }

            .tab-header {
                flex-direction: column;
            }

            .tab-button {
                width: 100%;
                justify-content: space-between;
                border-bottom: 1px solid var(--border-color);
                border-left: 4px solid transparent;
            }

            .tab-button.active {
                border-bottom-color: var(--border-color);
                border-left-color: var(--primary);
            }

            .table-container {
                max-height: 300px;
            }

            th, td {
                padding: var(--space-sm);
                font-size: 0.9rem;
            }

            .preview-content {
                max-height: 60px;
                font-size: 0.8rem;
            }
        }

        /* Fix for very small screens */
        @media (max-width: 480px) {
            .step-panel {
                padding: var(--space-md);
            }

            .step-title {
                font-size: 1.2rem;
            }

            .file-input-label {
                padding: var(--space-md);
                flex-direction: column;
                gap: var(--space-sm);
            }

            .file-input-label .material-icons-round {
                margin-right: 0;
            }

            th, td {
                padding: var(--space-xs);
                font-size: 0.8rem;
            }

            .btn {
                padding: var(--space-xs) var(--space-md);
                font-size: 0.9rem;
            }
        }

        /* Mode Selection Styles */
        .mode-selection {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .mode-option {
            cursor: pointer;
            display: block;
        }

        .mode-option input[type="radio"] {
            display: none;
        }

        .mode-card {
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: var(--radius-md);
            text-align: center;
            transition: all 0.3s ease;
            background-color: #fff;
        }

        .mode-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(67, 97, 238, 0.1);
        }

        .mode-option input[type="radio"]:checked + .mode-card {
            border-color: var(--primary-color);
            background-color: #f0f4ff;
            box-shadow: 0 4px 12px rgba(67, 97, 238, 0.15);
        }

        .mode-card .material-icons-round {
            font-size: 48px;
            color: var(--primary-color);
            margin-bottom: 10px;
            display: block;
        }

        .mode-card h3 {
            margin: 10px 0 5px 0;
            color: var(--text-primary);
            font-size: 18px;
        }

        .mode-card p {
            margin: 0;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .mode-content {
            margin-top: 20px;
        }

        /* API Key Styles */
        .api-key-container {
            position: relative;
            display: flex;
            align-items: center;
            width: 100%;
        }

        .api-key-container input {
            flex: 1;
            padding: 12px 50px 12px 16px;
            border: 1px solid #ddd;
            border-radius: var(--radius-md);
            font-size: 14px;
            background-color: #fff;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
            width: 100%;
            box-sizing: border-box;
        }

        .api-key-container input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
        }

        .api-key-container input::placeholder {
            color: var(--text-secondary);
        }

        .btn-icon {
            position: absolute;
            right: 12px;
            background: none;
            border: none;
            cursor: pointer;
            padding: 8px;
            border-radius: var(--radius-sm);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .btn-icon:hover {
            background-color: #f8f9fa;
            color: var(--text-primary);
        }

        .btn-icon .material-icons-round {
            font-size: 20px;
        }

        .api-key-actions {
            display: flex;
            gap: 12px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .api-key-actions .btn {
            flex: 1;
            min-width: 140px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-outline {
            background-color: transparent;
            border: 1px solid #ddd;
            color: var(--text-secondary);
        }

        .btn-outline:hover {
            background-color: #f8f9fa;
            border-color: #adb5bd;
            color: var(--text-primary);
        }

        .api-key-status {
            margin-top: 15px;
            padding: 12px 16px;
            border-radius: var(--radius-md);
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .api-key-status::before {
            content: '';
            width: 16px;
            height: 16px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .api-key-status.testing {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .api-key-status.testing::before {
            background-color: #ffc107;
            animation: pulse 1.5s infinite;
        }

        .api-key-status.valid {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .api-key-status.valid::before {
            background-color: #28a745;
        }

        .api-key-status.invalid {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .api-key-status.invalid::before {
            background-color: #dc3545;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* AI Content Settings Styles */
        .ai-content-settings {
            margin-top: 20px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: var(--radius-md);
            border: 1px solid #e9ecef;
        }

        .prompt-template {
            margin-bottom: 20px;
        }

        .prompt-template label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .prompt-template textarea {
            width: 100%;
            min-height: 80px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: var(--radius-sm);
            font-family: inherit;
            resize: vertical;
        }

        .template-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
        }

        .char-count {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .char-count.warning {
            color: #f59e0b;
        }

        .char-count.error {
            color: #ef4444;
        }

        .parameter-selection {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9f5ff;
            border-radius: var(--radius-md);
            border-left: 4px solid var(--primary-color);
        }

        .parameter-selection h4 {
            margin-bottom: 15px;
            color: var(--text-primary);
        }

        .parameter-checkboxes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <header>
            <h1>JSON Content Updater</h1>
            <p class="subtitle">Update your JSON files with CSV data in a few simple steps</p>
        </header>

        <div class="progress-container">
            <div class="progress-steps">
                <div class="progress-step active" id="step1-indicator">
                    <div class="step-circle">1</div>
                    <div class="step-label">Upload CSV</div>
                </div>
                <div class="progress-step" id="step2-indicator">
                    <div class="step-circle">2</div>
                    <div class="step-label">Upload JSON & Mapping</div>
                </div>
                <div class="progress-step" id="step3-indicator">
                    <div class="step-circle">3</div>
                    <div class="step-label">Map & Update</div>
                </div>
            </div>
        </div>

        <div id="status-message"></div>

        <!-- Step 1: Choose Data Source -->
        <div class="step-panel active" id="step1-panel">
            <h2 class="step-title"><span class="step-number">1</span>Choose Data Source</h2>
            <p class="step-description">Select how you want to provide content for your medical test mapping.</p>

            <!-- Mode Selection -->
            <div class="form-group">
                <label>Content Source</label>
                <div class="mode-selection">
                    <label class="mode-option">
                        <input type="radio" name="contentMode" value="csv" id="csvModeRadio" checked>
                        <div class="mode-card">
                            <span class="material-icons-round">upload_file</span>
                            <h3>Upload CSV Data</h3>
                            <p>Use existing CSV files with your test data</p>
                        </div>
                    </label>
                    <label class="mode-option">
                        <input type="radio" name="contentMode" value="ai" id="aiModeRadio">
                        <div class="mode-card">
                            <span class="material-icons-round">auto_awesome</span>
                            <h3>Generate Content with AI</h3>
                            <p>Use AI to automatically generate medical test content</p>
                        </div>
                    </label>
                </div>
            </div>

            <!-- CSV Mode Content -->
            <div id="csvModeContent" class="mode-content">
                <div class="form-group">
                    <label for="csvFile">CSV Data File</label>
                    <p class="form-description">Upload a CSV file containing your test data with headers that match the JSON structure you want to update.</p>

                    <div class="file-input-container">
                        <label class="file-input-label" for="csvFile">
                            <span class="material-icons-round">upload_file</span>
                            <span>Click to select or drag and drop CSV file</span>
                        </label>
                        <input type="file" id="csvFile" class="file-input" accept=".csv">
                    </div>
                    <div class="file-name" id="csvFileName"></div>
                </div>
            </div>

            <!-- AI Mode Content -->
            <div id="aiModeContent" class="mode-content" style="display: none;">
                <div class="form-group">
                    <label for="geminiApiKey">Gemini API Key</label>
                    <p class="form-description">Pre-configured Google Gemini API key for AI content generation.</p>

                    <div class="api-key-container">
                        <input type="password" id="geminiApiKey" placeholder="Pre-configured API key" readonly>
                        <button type="button" id="toggleApiKeyVisibility" class="btn-icon">
                            <span class="material-icons-round">visibility</span>
                        </button>
                    </div>

                    <div class="api-key-actions">
                        <button type="button" id="testApiKeyButton" class="btn btn-secondary">
                            <span class="material-icons-round">key</span>
                            Test API Key
                        </button>
                        <button type="button" id="clearApiKeyButton" class="btn btn-outline">
                            <span class="material-icons-round">delete</span>
                            Clear Saved Key
                        </button>
                    </div>

                    <div id="apiKeyStatus" class="api-key-status"></div>
                </div>
            </div>

            <div class="btn-container">
                <div></div> <!-- Empty div for flex spacing -->
                <button id="step1Next" class="btn btn-primary" disabled>
                    <span class="material-icons-round">arrow_forward</span>
                    Next
                </button>
            </div>
        </div>

        <!-- Step 2: Upload JSON & Mapping -->
        <div class="step-panel" id="step2-panel">
            <h2 class="step-title"><span class="step-number">2</span>Upload JSON & Mapping</h2>
            <p class="step-description">Select the JSON file you want to update and optionally upload a mapping file.</p>

            <!-- Mode Indicator -->
            <div id="step2ModeIndicator" class="mode-indicator" style="margin-bottom: 20px; padding: 10px; background-color: #e9f5ff; border-radius: var(--radius-md); border-left: 4px solid var(--primary-color);">
                <strong>Current Mode:</strong> <span id="step2ModeText">CSV Data Upload</span>
            </div>

            <div class="form-group">
                <h3 class="section-title">JSON File</h3>
                <div class="file-input-container">
                    <label class="file-input-label" for="jsonFile">
                        <span class="material-icons-round">upload_file</span>
                        <span>Click to select or drag and drop JSON file</span>
                    </label>
                    <input type="file" id="jsonFile" class="file-input" accept=".json">
                </div>
                <div class="file-name" id="jsonFileName"></div>
            </div>

            <div class="form-group">
                <h3 class="section-title">JSON Structure Configuration</h3>
                <p class="form-description">Specify how you want to update the JSON structure with CSV data.</p>

                <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: var(--radius-md);">
                    <h4 style="margin-bottom: 15px;">Update Options</h4>

                    <div style="margin-bottom: 15px;">
                        <label class="radio-label" style="display: block; margin-bottom: 10px;">
                            <input type="radio" name="jsonUpdateMode" id="jsonUpdateModeReplace" value="replace" checked>
                            <span>Replace existing fields (clear all suggestions and add new ones)</span>
                        </label>
                        <label class="radio-label" style="display: block; margin-bottom: 10px;">
                            <input type="radio" name="jsonUpdateMode" id="jsonUpdateModeAdd" value="add">
                            <span>Add to existing fields (keep existing suggestions and add new ones)</span>
                        </label>
                        <label class="radio-label" style="display: block;">
                            <input type="radio" name="jsonUpdateMode" id="jsonUpdateModeUpdate" value="update">
                            <span>Update existing fields only (only update fields that already exist)</span>
                        </label>
                    </div>

                    <h4 style="margin: 20px 0 15px 0;">Data Injection Instructions</h4>
                    <div style="background-color: #e9f5ff; padding: 15px; border-radius: 5px; border-left: 4px solid #4361ee; margin-bottom: 20px;">
                        <p style="margin: 0 0 10px 0; font-weight: 500;">Important: CSV Headers and JSON Field IDs</p>
                        <ul style="margin: 0; padding-left: 20px;">
                            <li>Your CSV headers will be used EXACTLY as they appear as the JSON field IDs</li>
                            <li>For example, if your CSV has a column named "Test Overview", the JSON field ID will be "Test Overview"</li>
                            <li>The data from each CSV column will be injected into the corresponding JSON field's content</li>
                            <li>Make sure your CSV headers match exactly what you want to appear in the JSON</li>
                        </ul>
                    </div>

                    <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107; margin-bottom: 20px;">
                        <p style="margin: 0 0 10px 0; font-weight: 500;">Example:</p>
                        <p style="margin: 0 0 10px 0;">If your CSV has these headers:</p>
                        <code style="display: block; background: #f8f9fa; padding: 10px; margin-bottom: 10px; border-radius: 5px; font-family: monospace;">Test, Test Overview, Advices</code>
                        <p style="margin: 0 0 10px 0;">Your JSON will have these fields:</p>
                        <code style="display: block; background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace;">
{<br>
&nbsp;&nbsp;"id": "Test Overview",<br>
&nbsp;&nbsp;"title": "Test Overview",<br>
&nbsp;&nbsp;"content": "Your CSV data for Test Overview"<br>
},<br>
{<br>
&nbsp;&nbsp;"id": "Advices",<br>
&nbsp;&nbsp;"title": "Advices",<br>
&nbsp;&nbsp;"content": "Your CSV data for Advices"<br>
}
                        </code>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label class="checkbox-label" style="display: block; margin-bottom: 10px;">
                            <input type="checkbox" id="useSimpleMapping" checked>
                            <span>Use exact CSV headers as JSON field IDs (recommended)</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- AI Content Settings (shown only in AI mode) -->
            <div id="aiContentSettings" class="ai-content-settings" style="display: none;">
                <h3 class="section-title">AI Content Settings</h3>
                <p class="form-description">Configure the AI prompts for generating medical test content. Use placeholders: {testName}, {reportName}, {testId}</p>

                <!-- Parameter Selection -->
                <div class="parameter-selection">
                    <h4>Select Parameters to Generate</h4>
                    <p style="margin-bottom: 15px; font-size: 14px; color: var(--text-secondary);">Choose which content categories you want the AI to generate for your medical tests.</p>
                    <div class="parameter-checkboxes">
                        <label class="checkbox-label">
                            <input type="checkbox" id="generateAboutTest" checked>
                            <span>About Test (40-50 words)</span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="generateCauseAbnormal" checked>
                            <span>Cause of Abnormal Result (40-50 words)</span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="generateImpactAbnormal" checked>
                            <span>Impact of Abnormal Result (40-50 words)</span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="generateHowToImprove" checked>
                            <span>How to Improve (40-50 words)</span>
                        </label>
                    </div>
                </div>

                <!-- Prompt Templates -->
                <div class="prompt-template">
                    <label for="aboutTestPrompt">About Test Prompt Template</label>
                    <textarea id="aboutTestPrompt" placeholder="Enter prompt template for 'About Test' content...">Generate a concise medical explanation (40-50 words) about the {testName} test. Explain what this test measures and its primary purpose in medical diagnosis. Focus on the biological marker or parameter being tested and its clinical significance.</textarea>
                    <div class="template-actions">
                        <span class="char-count" id="aboutTestCharCount">0 characters</span>
                        <button type="button" class="btn btn-outline btn-sm" onclick="resetPromptTemplate('aboutTestPrompt')">Reset to Default</button>
                    </div>
                </div>

                <div class="prompt-template">
                    <label for="causeAbnormalPrompt">Cause of Abnormal Result Prompt Template</label>
                    <textarea id="causeAbnormalPrompt" placeholder="Enter prompt template for 'Cause of Abnormal Result' content...">Explain the medical causes (40-50 words) of abnormal {testName} results. Include common conditions, lifestyle factors, medications, or diseases that can lead to elevated or decreased levels. Focus on clinically relevant causes that healthcare providers should consider.</textarea>
                    <div class="template-actions">
                        <span class="char-count" id="causeAbnormalCharCount">0 characters</span>
                        <button type="button" class="btn btn-outline btn-sm" onclick="resetPromptTemplate('causeAbnormalPrompt')">Reset to Default</button>
                    </div>
                </div>

                <div class="prompt-template">
                    <label for="impactAbnormalPrompt">Impact of Abnormal Result Prompt Template</label>
                    <textarea id="impactAbnormalPrompt" placeholder="Enter prompt template for 'Impact of Abnormal Result' content...">Describe the health implications (40-50 words) of abnormal {testName} results. Explain potential symptoms, health risks, or complications that may arise from elevated or decreased levels. Focus on patient-relevant impacts and when medical attention is needed.</textarea>
                    <div class="template-actions">
                        <span class="char-count" id="impactAbnormalCharCount">0 characters</span>
                        <button type="button" class="btn btn-outline btn-sm" onclick="resetPromptTemplate('impactAbnormalPrompt')">Reset to Default</button>
                    </div>
                </div>

                <div class="prompt-template">
                    <label for="howToImprovePrompt">How to Improve Prompt Template</label>
                    <textarea id="howToImprovePrompt" placeholder="Enter prompt template for 'How to Improve' content...">Provide actionable recommendations (40-50 words) for improving abnormal {testName} results. Include dietary changes, lifestyle modifications, supplements, or medical treatments. Focus on evidence-based interventions that patients can implement with healthcare provider guidance.</textarea>
                    <div class="template-actions">
                        <span class="char-count" id="howToImproveCharCount">0 characters</span>
                        <button type="button" class="btn btn-outline btn-sm" onclick="resetPromptTemplate('howToImprovePrompt')">Reset to Default</button>
                    </div>
                </div>

                <!-- Test Content Generation -->
                <div style="margin-top: 20px; padding: 15px; background-color: #e9f5ff; border-radius: var(--radius-md); border-left: 4px solid var(--primary-color);">
                    <h4 style="margin-bottom: 10px;">Test Content Generation</h4>
                    <p style="margin-bottom: 15px; font-size: 14px; color: var(--text-secondary);">Test your API key and prompt templates with a sample medical test.</p>
                    <button type="button" id="testContentGeneration" class="btn btn-secondary" disabled>
                        <span class="material-icons-round">science</span>
                        Test Generate Content
                    </button>
                    <div id="testGenerationResult" style="margin-top: 10px; display: none;"></div>
                </div>
            </div>

            <div class="form-group">
                <h3 class="section-title">Mapping File (Optional)</h3>
                <p class="form-description">Upload a CSV or Excel file that maps JSON test names to CSV test names. If you don't have a mapping file, you can create one by clicking the button below.</p>

                <div class="mapping-options">
                    <div class="file-input-container">
                        <label class="file-input-label" for="mappingFile">
                            <span class="material-icons-round">upload_file</span>
                            <span>Click to select or drag and drop mapping file</span>
                        </label>
                        <input type="file" id="mappingFile" class="file-input" accept=".csv,.xlsx,.xls">
                    </div>
                    <div class="file-name" id="mappingFileName"></div>

                    <div class="template-options">
                        <button id="generateTemplateButton" class="btn btn-secondary" disabled>
                            <span class="material-icons-round">download</span>
                            Download Template
                        </button>
                        <select id="templateFormat" class="template-format">
                            <option value="xlsx">Excel (.xlsx)</option>
                            <option value="csv">CSV (.csv)</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="btn-container">
                <button id="step2Back" class="btn btn-secondary">
                    <span class="material-icons-round">arrow_back</span>
                    Back
                </button>
                <button id="step2Next" class="btn btn-primary" disabled>
                    <span class="material-icons-round">arrow_forward</span>
                    Next
                </button>
            </div>
        </div>

        <!-- Step 3: Map & Update -->
        <div class="step-panel" id="step3-panel">
            <h2 class="step-title"><span class="step-number">3</span>Map & Update</h2>
            <p class="step-description">Map JSON parameter names to CSV data, select fields to update, and generate your updated JSON.</p>

            <div class="form-group">
                <h3 class="section-title">Fields to Update</h3>
                <div class="checkbox-group" id="fieldsCheckboxGroup">
                    <!-- Field checkboxes will be dynamically generated based on CSV headers -->
                    <div class="loading-fields">
                        <span class="material-icons-round loading-spinner">sync</span>
                        <span>Loading available fields...</span>
                    </div>
                </div>
            </div>

            <div class="tabs">
                <div class="tab-header">
                    <div class="tab-button active" data-tab="mapped-tab">
                        <span class="material-icons-round">check_circle</span>
                        <span>Mapped Parameters</span>
                        <span class="badge" id="mapped-count">0</span>
                    </div>
                    <div class="tab-button" data-tab="unmapped-tab">
                        <span class="material-icons-round">error</span>
                        <span>Unmapped Parameters</span>
                        <span class="badge" id="unmapped-count">0</span>
                    </div>
                </div>

                <div class="tab-content active" id="mapped-tab">
                    <div class="search-filter">
                        <div class="search-container">
                            <span class="material-icons-round">search</span>
                            <input type="text" id="mappedSearchInput" placeholder="Search mapped parameters...">
                            <button id="mappedClearButton" class="clear-button">
                                <span class="material-icons-round">close</span>
                            </button>
                        </div>

                    </div>

                    <div class="table-container">
                        <table id="mappedTable">
                            <thead>
                                <tr>
                                    <th class="checkbox-cell">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="selectAllMappedCheckbox">
                                            <span></span>
                                        </label>
                                    </th>
                                    <th>JSON Parameter Name</th>
                                    <th>CSV Parameter Name</th>
                                    <th>Content Preview</th>
                                </tr>
                            </thead>
                            <tbody id="mappedTableBody">
                                <!-- Mapped parameters will be added here dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="tab-content" id="unmapped-tab">
                    <div class="search-filter">
                        <div class="search-container">
                            <span class="material-icons-round">search</span>
                            <input type="text" id="unmappedSearchInput" placeholder="Search unmapped parameters...">
                            <button id="unmappedClearButton" class="clear-button">
                                <span class="material-icons-round">close</span>
                            </button>
                        </div>

                    </div>

                    <div class="table-container">
                        <table id="unmappedTable">
                            <thead>
                                <tr>
                                    <th>JSON Parameter Name</th>
                                    <th>Map to CSV Parameter</th>
                                </tr>
                            </thead>
                            <tbody id="unmappedTableBody">
                                <!-- Unmapped parameters will be added here dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="action-panel">
                <div class="section-header">
                    <h3 class="section-title">Download Options</h3>
                </div>

                <div class="download-options">
                    <button id="downloadAllDataButton" class="btn btn-secondary">
                        <span class="material-icons-round">download</span>
                        Download All Data
                    </button>

                    <button id="exportCsvButton" class="btn btn-secondary">
                        <span class="material-icons-round">table_view</span>
                        Export CSV
                    </button>

                    <button id="clearBlankTestsButton" class="btn btn-warning">
                        <span class="material-icons-round">cleaning_services</span>
                        Clear Unmapped Tests
                    </button>

                    <button id="replaceLoremButton" class="btn btn-danger">
                        <span class="material-icons-round">find_replace</span>
                        Replace All Lorem Ipsum
                    </button>

                    <button id="downloadAiDataButton" class="btn btn-success" style="display: none;">
                        <span class="material-icons-round">table_chart</span>
                        Download AI Generated Data
                    </button>
                </div>

                <!-- CSV Export Options -->
                <div id="csvExportOptions" class="form-group" style="display: none; margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: var(--radius-md);">
                    <h4 style="margin-bottom: 15px;">CSV Export Options</h4>

                    <div style="margin-bottom: 15px;">
                        <label class="checkbox-label">
                            <input type="checkbox" id="useTemplateCheckbox">
                            <span>Use custom template for CSV export</span>
                        </label>
                    </div>

                    <div id="templateUploadContainer" style="display: none; margin-bottom: 15px;">
                        <p class="form-description">Upload a CSV template file with your desired headers. The data will be mapped to match your template.</p>

                        <div class="file-input-container" style="margin-bottom: 10px;">
                            <label class="file-input-label" for="csvTemplateFile">
                                <span class="material-icons-round">upload_file</span>
                                <span>Click to select or drag and drop CSV template</span>
                            </label>
                            <input type="file" id="csvTemplateFile" class="file-input" accept=".csv">
                        </div>
                        <div class="file-name" id="csvTemplateFileName"></div>
                    </div>

                    <div style="display: flex; gap: 10px; margin-top: 15px;">
                        <button id="downloadCsvButton" class="btn btn-primary" disabled>
                            <span class="material-icons-round">download</span>
                            Download CSV
                        </button>
                        <button id="cancelCsvExportButton" class="btn btn-secondary">
                            <span class="material-icons-round">close</span>
                            Cancel
                        </button>
                    </div>
                </div>

                <div id="updateProgress" class="progress-bar-container" style="display: none;">
                    <h3>Updating JSON...</h3>
                    <div class="progress-bar">
                        <div id="progressBar" class="progress-bar-fill"></div>
                    </div>
                    <p id="progressText">0%</p>
                </div>

                <div id="updateComplete" class="update-complete" style="display: none;">
                    <div class="status status-success">
                        <span class="material-icons-round">check_circle</span>
                        <div>
                            <strong>Success!</strong> Your JSON file has been updated successfully.
                        </div>
                    </div>

                    <div class="summary-container">
                        <h3>Summary of Changes</h3>
                        <ul id="changeSummary">
                            <!-- Summary items will be added here dynamically -->
                        </ul>
                    </div>
                </div>
            </div>

            <div class="btn-container">
                <button id="step3Back" class="btn btn-secondary">
                    <span class="material-icons-round">arrow_back</span>
                    Back
                </button>
                <button id="updateButton" class="btn btn-success">
                    <span class="material-icons-round">update</span>
                    Update JSON
                </button>
                <button id="downloadButton" class="btn btn-primary" style="display: none;">
                    <span class="material-icons-round">download</span>
                    Download Updated JSON
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.2/papaparse.min.js"></script>
    <script>
        // Global variables
        let csvData = null;
        let jsonData = null;
        let updatedJsonData = null;
        let csvFileName = '';
        let jsonFileName = '';
        let csvTemplateFileName = '';
        let csvTemplateData = null;
        let testNameMappings = {}; // Maps JSON test names to CSV test names
        let csvTestData = {}; // Stores CSV data by test name

        // AI mode variables
        let currentContentMode = 'csv'; // 'csv' or 'ai'
        let geminiApiKey = 'AIzaSyCIJjnnhAlEiVnYFn66LOSa4QoceuY4YI8'; // Pre-set API key
        let isApiKeyValid = true; // Pre-validated API key
        let aiGeneratedData = {}; // Stores AI-generated content by test name
        let aiGenerationComplete = false; // Track if AI generation is complete
        let aiGeneratedTests = []; // Array to store structured AI-generated data for export

        // Default prompt templates
        const defaultPrompts = {
            aboutTest: "Generate a concise medical explanation (40-50 words) about the {testName} test. Explain what this test measures and its primary purpose in medical diagnosis. Focus on the biological marker or parameter being tested and its clinical significance.",
            causeAbnormal: "Explain the medical causes (40-50 words) of abnormal {testName} results. Include common conditions, lifestyle factors, medications, or diseases that can lead to elevated or decreased levels. Focus on clinically relevant causes that healthcare providers should consider.",
            impactAbnormal: "Describe the health implications (40-50 words) of abnormal {testName} results. Explain potential symptoms, health risks, or complications that may arise from elevated or decreased levels. Focus on patient-relevant impacts and when medical attention is needed.",
            howToImprove: "Provide actionable recommendations (40-50 words) for improving abnormal {testName} results. Include dietary changes, lifestyle modifications, supplements, or medical treatments. Focus on evidence-based interventions that patients can implement with healthcare provider guidance."
        };

        // DOM elements
        const csvFileInput = document.getElementById('csvFile');
        const jsonFileInput = document.getElementById('jsonFile');
        const mappingFileInput = document.getElementById('mappingFile');
        const csvFileNameDisplay = document.getElementById('csvFileName');
        const jsonFileNameDisplay = document.getElementById('jsonFileName');
        const mappingFileNameDisplay = document.getElementById('mappingFileName');
        const generateTemplateButton = document.getElementById('generateTemplateButton');
        const templateFormatSelect = document.getElementById('templateFormat');
        const statusMessage = document.getElementById('status-message');

        // JSON structure configuration elements
        const jsonUpdateModeReplace = document.getElementById('jsonUpdateModeReplace');
        const jsonUpdateModeAdd = document.getElementById('jsonUpdateModeAdd');
        const jsonUpdateModeUpdate = document.getElementById('jsonUpdateModeUpdate');
        const useSimpleMapping = document.getElementById('useSimpleMapping');

        // CSV export elements
        const exportCsvButton = document.getElementById('exportCsvButton');
        const csvExportOptions = document.getElementById('csvExportOptions');
        const useTemplateCheckbox = document.getElementById('useTemplateCheckbox');
        const templateUploadContainer = document.getElementById('templateUploadContainer');
        const csvTemplateFileInput = document.getElementById('csvTemplateFile');
        const csvTemplateFileNameDisplay = document.getElementById('csvTemplateFileName');
        const downloadCsvButton = document.getElementById('downloadCsvButton');
        const cancelCsvExportButton = document.getElementById('cancelCsvExportButton');

        // Step 3 elements
        const mappedTableBody = document.getElementById('mappedTableBody');
        const unmappedTableBody = document.getElementById('unmappedTableBody');
        const selectAllMappedCheckbox = document.getElementById('selectAllMappedCheckbox');
        const mappedSearchInput = document.getElementById('mappedSearchInput');
        const mappedClearButton = document.getElementById('mappedClearButton');
        const unmappedSearchInput = document.getElementById('unmappedSearchInput');
        const unmappedClearButton = document.getElementById('unmappedClearButton');
        const downloadAllDataButton = document.getElementById('downloadAllDataButton');
        const mappedCountBadge = document.getElementById('mapped-count');
        const unmappedCountBadge = document.getElementById('unmapped-count');

        // Update elements
        const updateButton = document.getElementById('updateButton');
        const downloadButton = document.getElementById('downloadButton');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const updateProgress = document.getElementById('updateProgress');
        const updateComplete = document.getElementById('updateComplete');
        const changeSummary = document.getElementById('changeSummary');

        // Step navigation elements
        const stepPanels = document.querySelectorAll('.step-panel');
        const stepIndicators = document.querySelectorAll('.progress-step');

        // Step 1 navigation
        document.getElementById('step1Next').addEventListener('click', () => {
            goToStep(2);
        });

        // Step 2 navigation
        document.getElementById('step2Back').addEventListener('click', () => {
            goToStep(1);
        });
        document.getElementById('step2Next').addEventListener('click', () => {
            goToStep(3);
        });

        // Step 3 navigation
        document.getElementById('step3Back').addEventListener('click', () => {
            goToStep(2);
        });

        // Tab navigation
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabId = button.getAttribute('data-tab');

                // Remove active class from all buttons and contents
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));

                // Add active class to clicked button and corresponding content
                button.classList.add('active');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // File input event listeners
        csvFileInput.addEventListener('change', handleCsvFileSelect);
        jsonFileInput.addEventListener('change', handleJsonFileSelect);
        mappingFileInput.addEventListener('change', handleMappingFileSelect);
        generateTemplateButton.addEventListener('click', generateMappingTemplate);

        // Search event listeners
        mappedSearchInput.addEventListener('input', () => searchTests(mappedSearchInput.value, mappedTableBody));
        mappedClearButton.addEventListener('click', () => clearSearch(mappedSearchInput, mappedTableBody));
        unmappedSearchInput.addEventListener('input', () => searchTests(unmappedSearchInput.value, unmappedTableBody));
        unmappedClearButton.addEventListener('click', () => clearSearch(unmappedSearchInput, unmappedTableBody));

        // Download button
        downloadAllDataButton.addEventListener('click', downloadAllData);

        // Update buttons
        updateButton.addEventListener('click', updateJson);
        downloadButton.addEventListener('click', downloadUpdatedJson);

        // CSV export buttons
        exportCsvButton.addEventListener('click', toggleCsvExportOptions);
        useTemplateCheckbox.addEventListener('change', toggleTemplateUpload);
        csvTemplateFileInput.addEventListener('change', handleCsvTemplateFileSelect);
        downloadCsvButton.addEventListener('click', exportCsvData);
        cancelCsvExportButton.addEventListener('click', toggleCsvExportOptions);

        // Clear Blank Tests button
        document.getElementById('clearBlankTestsButton').addEventListener('click', clearBlankTests);

        // Replace Lorem Ipsum button
        document.getElementById('replaceLoremButton').addEventListener('click', replaceLoremIpsum);

        // Download AI Generated Data button
        document.getElementById('downloadAiDataButton').addEventListener('click', downloadAiGeneratedData);

        // Mode selection event listeners
        document.getElementById('csvModeRadio').addEventListener('change', handleModeChange);
        document.getElementById('aiModeRadio').addEventListener('change', handleModeChange);

        // AI mode event listeners
        document.getElementById('geminiApiKey').addEventListener('input', handleApiKeyInput);
        document.getElementById('toggleApiKeyVisibility').addEventListener('click', toggleApiKeyVisibility);
        document.getElementById('testApiKeyButton').addEventListener('click', testApiKey);
        document.getElementById('clearApiKeyButton').addEventListener('click', clearApiKey);
        document.getElementById('testContentGeneration').addEventListener('click', testContentGeneration);

        // Prompt template event listeners
        document.getElementById('aboutTestPrompt').addEventListener('input', updateCharCount);
        document.getElementById('causeAbnormalPrompt').addEventListener('input', updateCharCount);
        document.getElementById('impactAbnormalPrompt').addEventListener('input', updateCharCount);
        document.getElementById('howToImprovePrompt').addEventListener('input', updateCharCount);

        // Checkbox event listeners
        selectAllMappedCheckbox.addEventListener('change', toggleSelectAllMapped);

        // Initialize the application when the page loads
        document.addEventListener('DOMContentLoaded', initializeApp);

        // Also initialize when the script loads (fallback)
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeApp);
        } else {
            initializeApp();
        }

        // Initialize the application
        function initializeApp() {
            // Pre-populate and save the API key
            const apiKeyInput = document.getElementById('geminiApiKey');
            if (apiKeyInput) {
                apiKeyInput.value = geminiApiKey;
                apiKeyInput.disabled = true; // Disable input to prevent changes
                apiKeyInput.style.backgroundColor = '#f8f9fa'; // Visual indication it's disabled

                // Save the pre-set API key
                saveApiKey(geminiApiKey);

                // Show pre-validated status
                showApiKeyStatus('Pre-configured API key is ready for use.', 'valid');

                // Hide the test and clear buttons since key is pre-validated
                const testButton = document.getElementById('testApiKeyButton');
                const clearButton = document.getElementById('clearApiKeyButton');
                if (testButton) testButton.style.display = 'none';
                if (clearButton) clearButton.style.display = 'none';
            }

            // Initialize character counts for prompt templates
            updateCharCount({ target: document.getElementById('aboutTestPrompt') });
            updateCharCount({ target: document.getElementById('causeAbnormalPrompt') });
            updateCharCount({ target: document.getElementById('impactAbnormalPrompt') });
            updateCharCount({ target: document.getElementById('howToImprovePrompt') });

            // Initialize button states
            updateStep1NextButton();
            updateStep2NextButton();

            // Set initial mode state
            handleModeChange();
        }

        // Handle mode change between CSV and AI
        function handleModeChange() {
            const csvMode = document.getElementById('csvModeRadio').checked;
            const aiMode = document.getElementById('aiModeRadio').checked;

            currentContentMode = csvMode ? 'csv' : 'ai';

            // Show/hide appropriate content sections in Step 1
            document.getElementById('csvModeContent').style.display = csvMode ? 'block' : 'none';
            document.getElementById('aiModeContent').style.display = aiMode ? 'block' : 'none';

            // Show/hide AI content settings in Step 2
            const aiContentSettings = document.getElementById('aiContentSettings');
            if (aiContentSettings) {
                aiContentSettings.style.display = aiMode ? 'block' : 'none';
            }

            // Update step button states
            updateStep1NextButton();
            updateStep2NextButton();

            // Update progress step labels
            const step1Label = document.querySelector('#step1-indicator .step-label');
            step1Label.textContent = csvMode ? 'Upload CSV' : 'AI Setup';

            // Update Step 2 description and mode indicator based on mode
            const step2Description = document.querySelector('#step2-panel .step-description');
            const step2ModeText = document.getElementById('step2ModeText');

            if (step2Description) {
                if (csvMode) {
                    step2Description.textContent = 'Select the JSON file you want to update and optionally upload a mapping file.';
                } else {
                    step2Description.textContent = 'Select the JSON file you want to update and configure AI content generation settings.';
                }
            }

            if (step2ModeText) {
                step2ModeText.textContent = csvMode ? 'CSV Data Upload' : 'AI Content Generation';
            }
        }

        // Update Step 1 Next button state based on current mode
        function updateStep1NextButton() {
            const step1Next = document.getElementById('step1Next');
            const testContentButton = document.getElementById('testContentGeneration');

            if (currentContentMode === 'csv') {
                // CSV mode: enable if CSV file is uploaded
                step1Next.disabled = !csvData;
            } else {
                // AI mode: always enable (no API key validation required for navigation)
                step1Next.disabled = false;
                if (testContentButton) {
                    testContentButton.disabled = !isApiKeyValid; // Keep validation for test button
                }
            }
        }

        // Update Step 2 Next button state based on current mode and required conditions
        function updateStep2NextButton() {
            const step2Next = document.getElementById('step2Next');

            if (currentContentMode === 'csv') {
                // CSV mode: enable if BOTH CSV and JSON files are uploaded and valid
                const csvValid = csvData && csvData.length > 0;
                const jsonValid = jsonData && jsonData.summary_items && jsonData.summary_items.length > 0;

                step2Next.disabled = !(csvValid && jsonValid);

                // Update status message based on what's missing
                if (!csvValid && !jsonValid) {
                    showStatus('Please upload both CSV file (Step 1) and JSON file (Step 2) to continue.', 'info');
                } else if (!csvValid) {
                    showStatus('Please upload a CSV file in Step 1 to continue.', 'warning');
                } else if (!jsonValid) {
                    showStatus('Please upload a valid JSON file to continue.', 'warning');
                } else {
                    showStatus('Both files uploaded successfully. Ready to proceed to Step 3.', 'success');
                }
            } else {
                // AI mode: enable if JSON file is uploaded (no API key validation required for navigation)
                const jsonValid = jsonData && jsonData.summary_items && jsonData.summary_items.length > 0;

                step2Next.disabled = !jsonValid;

                // Update status message based on what's missing
                if (!jsonValid) {
                    showStatus('Please upload a valid JSON file to continue.', 'warning');
                } else {
                    showStatus('JSON file uploaded. Ready to proceed to Step 3 for AI content generation.', 'success');
                }
            }
        }

        // Test content generation with a sample medical test
        async function testContentGeneration() {
            const button = document.getElementById('testContentGeneration');
            const resultDiv = document.getElementById('testGenerationResult');

            if (!isApiKeyValid) {
                showTestGenerationResult('Please test and validate your API key first.', 'error');
                return;
            }

            // Show loading state
            button.disabled = true;
            button.innerHTML = '<span class="material-icons-round">sync</span> Generating...';
            showTestGenerationResult('Generating sample content...', 'testing');

            try {
                // Get the current prompt template
                const aboutTestPrompt = document.getElementById('aboutTestPrompt').value;

                // Test with a sample medical test
                const sampleTestName = 'Hemoglobin A1C';
                const sampleReportName = 'HbA1c';
                const sampleTestId = '12345';

                const generatedContent = await generateContentWithGemini(
                    aboutTestPrompt,
                    sampleTestName,
                    sampleReportName,
                    sampleTestId
                );

                showTestGenerationResult(
                    `✅ Success! Generated content for "${sampleTestName}":\n\n"${generatedContent}"`,
                    'success'
                );

            } catch (error) {
                console.error('Test content generation failed:', error);
                showTestGenerationResult(
                    `❌ Content generation failed: ${error.message}`,
                    'error'
                );
            } finally {
                // Reset button
                button.disabled = false;
                button.innerHTML = '<span class="material-icons-round">science</span> Test Generate Content';
            }
        }

        // Show test generation result
        function showTestGenerationResult(message, type) {
            const resultDiv = document.getElementById('testGenerationResult');
            resultDiv.style.display = 'block';
            resultDiv.className = `api-key-status ${type}`;
            resultDiv.style.marginTop = '10px';
            resultDiv.style.whiteSpace = 'pre-wrap';
            resultDiv.textContent = message;
        }

        // Generate AI content for selected tests only
        async function generateAiContentForSelectedTests() {
            if (!jsonData || !jsonData.summary_items) {
                showStatus('Please upload a JSON file first.', 'error');
                return;
            }

            // Runtime API key validation
            try {
                await validateApiKeyAtRuntime();
            } catch (error) {
                showStatus('API key is not valid. Content generation cannot proceed.', 'error');
                console.error('API key validation failed:', error);
                return;
            }

            // Get selected tests from mapped table checkboxes
            const selectedTestIndices = [];
            mappedTableBody.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
                selectedTestIndices.push(parseInt(checkbox.dataset.index));
            });

            if (selectedTestIndices.length === 0) {
                showStatus('No tests selected for AI content generation. Please select at least one test in the Mapped Parameters table.', 'error');
                return;
            }

            // Reset AI generation data
            aiGeneratedTests = [];
            aiGenerationComplete = false;
            updateAiDownloadButton();

            // Get selected content types
            const generateAbout = document.getElementById('generateAboutTest').checked;
            const generateCause = document.getElementById('generateCauseAbnormal').checked;
            const generateImpact = document.getElementById('generateImpactAbnormal').checked;
            const generateImprove = document.getElementById('generateHowToImprove').checked;

            if (!generateAbout && !generateCause && !generateImpact && !generateImprove) {
                showStatus('Please select at least one content type to generate in Step 2.', 'error');
                return;
            }

            // Get prompt templates
            const prompts = {
                aboutTest: document.getElementById('aboutTestPrompt').value,
                causeAbnormal: document.getElementById('causeAbnormalPrompt').value,
                impactAbnormal: document.getElementById('impactAbnormalPrompt').value,
                howToImprove: document.getElementById('howToImprovePrompt').value
            };

            // Filter tests to only include selected ones
            const selectedTests = selectedTestIndices.map(index => jsonData.summary_items[index]).filter(test => test);
            const selectedContentTypes = [generateAbout, generateCause, generateImpact, generateImprove].filter(Boolean).length;
            const totalOperations = selectedTests.length * selectedContentTypes;
            let completedOperations = 0;

            // Show progress
            updateProgress.style.display = 'block';
            updateButton.disabled = true;

            console.log(`Starting AI content generation for ${selectedTests.length} selected tests`);
            console.log(`Selected test indices:`, selectedTestIndices);
            console.log(`Content types to generate:`, { generateAbout, generateCause, generateImpact, generateImprove });

            try {
                for (let i = 0; i < selectedTests.length; i++) {
                    const test = selectedTests[i];
                    const testIndex = selectedTestIndices[i];
                    const testName = test.testName || 'Unknown Test';
                    const testId = test.testId || test.id || '';
                    const reportName = test.reportName || testName;

                    console.log(`Generating content for test ${i + 1}/${selectedTests.length}: ${testName} (index: ${testIndex})`);

                    // Generate content for each selected type
                    const contentTypes = [
                        { key: 'aboutTest', enabled: generateAbout, prompt: prompts.aboutTest },
                        { key: 'causeAbnormal', enabled: generateCause, prompt: prompts.causeAbnormal },
                        { key: 'impactAbnormal', enabled: generateImpact, prompt: prompts.impactAbnormal },
                        { key: 'howToImprove', enabled: generateImprove, prompt: prompts.howToImprove }
                    ];

                    for (const contentType of contentTypes) {
                        if (contentType.enabled) {
                            try {
                                const generatedContent = await generateContentWithGemini(
                                    contentType.prompt,
                                    testName,
                                    reportName,
                                    testId
                                );

                                // Add to export data structure
                                addAiGeneratedTest(testId, testName, contentType.key, generatedContent);

                                // Add to JSON structure for regular workflow
                                if (!test.suggestions) {
                                    test.suggestions = [];
                                }

                                // Create suggestion object
                                const suggestion = {
                                    id: `${contentType.key}-ai-generated`,
                                    title: getContentTypeTitle(contentType.key),
                                    content: generatedContent
                                };

                                test.suggestions.push(suggestion);

                                console.log(`Generated ${contentType.key} for ${testName}: ${generatedContent.substring(0, 50)}...`);

                            } catch (error) {
                                console.error(`Failed to generate ${contentType.key} for ${testName}:`, error);
                                // Continue with other content types
                            }

                            completedOperations++;
                            const progress = Math.round((completedOperations / totalOperations) * 100);
                            progressBar.style.width = `${progress}%`;
                            progressText.textContent = `${progress}% (${completedOperations}/${totalOperations})`;

                            // Add delay to respect rate limits
                            await new Promise(resolve => setTimeout(resolve, 500));
                        }
                    }
                }

                // Mark generation as complete
                aiGenerationComplete = true;
                updatedJsonData = JSON.parse(JSON.stringify(jsonData));

                // Show completion
                updateProgress.style.display = 'none';
                updateComplete.style.display = 'block';
                downloadButton.style.display = 'block';
                updateAiDownloadButton();

                // Calculate content type breakdown
                const contentTypeBreakdown = [];
                if (generateAbout) contentTypeBreakdown.push('About Test');
                if (generateCause) contentTypeBreakdown.push('Cause of Abnormal');
                if (generateImpact) contentTypeBreakdown.push('Impact of Abnormal');
                if (generateImprove) contentTypeBreakdown.push('How to Improve');

                // Update summary
                changeSummary.innerHTML = `
                    <li>Generated AI content for ${selectedTests.length} selected tests</li>
                    <li>Content types generated: ${contentTypeBreakdown.join(', ')}</li>
                    <li>Total content pieces generated: ${completedOperations}</li>
                    <li>Tests available for export: ${aiGeneratedTests.length}</li>
                    <li>Files ready for download</li>
                `;

                showStatus(`Successfully generated AI content for ${selectedTests.length} selected tests (${completedOperations} content pieces)!`, 'success');

            } catch (error) {
                console.error('AI content generation failed:', error);
                showStatus(`AI content generation failed for selected tests: ${error.message}`, 'error');
            } finally {
                updateButton.disabled = false;
                updateProgress.style.display = 'none';
            }
        }

        // Helper function to get content type title
        function getContentTypeTitle(contentType) {
            const titles = {
                aboutTest: 'About Test (AI Generated)',
                causeAbnormal: 'Cause of Abnormal Result (AI Generated)',
                impactAbnormal: 'Impact of Abnormal Result (AI Generated)',
                howToImprove: 'How to Improve (AI Generated)'
            };
            return titles[contentType] || 'AI Generated Content';
        }

        // Handle API key input (disabled for pre-configured key)
        function handleApiKeyInput(event) {
            // API key is pre-configured and disabled, so this function does minimal work
            const apiKey = event.target.value.trim();
            geminiApiKey = apiKey;

            // Enable/disable test button
            document.getElementById('testApiKeyButton').disabled = !apiKey;

            // Note: Button states no longer depend on API key validation for navigation
        }

        // Toggle API key visibility
        function toggleApiKeyVisibility() {
            const input = document.getElementById('geminiApiKey');
            const button = document.getElementById('toggleApiKeyVisibility');
            const icon = button.querySelector('.material-icons-round');

            if (input.type === 'password') {
                input.type = 'text';
                icon.textContent = 'visibility_off';
            } else {
                input.type = 'password';
                icon.textContent = 'visibility';
            }
        }

        // Test API key
        async function testApiKey() {
            const button = document.getElementById('testApiKeyButton');
            const statusDiv = document.getElementById('apiKeyStatus');

            if (!geminiApiKey) {
                showApiKeyStatus('Please enter an API key first.', 'invalid');
                return;
            }

            // Show testing status
            button.disabled = true;
            button.innerHTML = '<span class="material-icons-round">sync</span> Testing...';
            showApiKeyStatus('Testing API key...', 'testing');

            try {
                // Test the API key with a simple request using the correct endpoint
                const response = await fetch(`https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent?key=${geminiApiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: "Test message. Please respond with 'API key is working.'"
                            }]
                        }],
                        generationConfig: {
                            temperature: 0.7,
                            topK: 40,
                            topP: 0.95,
                            maxOutputTokens: 100
                        }
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('API Response:', data);

                    if (data.candidates && data.candidates.length > 0 && data.candidates[0].content) {
                        isApiKeyValid = true;
                        const responseText = data.candidates[0].content.parts[0].text;
                        showApiKeyStatus(`API key is valid! Response: "${responseText.substring(0, 50)}..."`, 'valid');
                        saveApiKey(geminiApiKey);
                        // Note: Button states no longer depend on API key validation for navigation
                    } else {
                        throw new Error('Invalid response structure from API');
                    }
                } else {
                    const errorData = await response.json();
                    console.error('API Error Response:', errorData);

                    let errorMessage = 'API key validation failed';
                    if (errorData.error) {
                        if (errorData.error.message) {
                            errorMessage = errorData.error.message;
                        } else if (errorData.error.details) {
                            errorMessage = errorData.error.details[0]?.reason || errorMessage;
                        }
                    }

                    // Handle specific error cases
                    if (response.status === 400) {
                        errorMessage = 'Invalid API key or request format';
                    } else if (response.status === 403) {
                        errorMessage = 'API key does not have permission to access Gemini API';
                    } else if (response.status === 404) {
                        errorMessage = 'Gemini API endpoint not found - check API key permissions';
                    } else if (response.status === 429) {
                        errorMessage = 'API rate limit exceeded - please try again later';
                    }

                    throw new Error(errorMessage);
                }
            } catch (error) {
                console.error('API key test failed:', error);
                isApiKeyValid = false;

                let displayMessage = error.message;
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    displayMessage = 'Network error - please check your internet connection';
                }

                showApiKeyStatus(`API test failed: ${displayMessage}`, 'invalid');
                // Note: Button states no longer depend on API key validation for navigation
            } finally {
                // Reset button
                button.disabled = false;
                button.innerHTML = '<span class="material-icons-round">key</span> Test API Key';
            }
        }

        // Helper function for API calls with retry logic
        async function makeGeminiApiCall(prompt, retries = 3, delay = 1000) {
            for (let attempt = 0; attempt < retries; attempt++) {
                try {
                    const response = await fetch(`https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent?key=${geminiApiKey}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            contents: [{
                                parts: [{
                                    text: prompt
                                }]
                            }],
                            generationConfig: {
                                temperature: 0.7,
                                topK: 40,
                                topP: 0.95,
                                maxOutputTokens: 150,
                                stopSequences: []
                            },
                            safetySettings: [
                                {
                                    category: "HARM_CATEGORY_HARASSMENT",
                                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                                },
                                {
                                    category: "HARM_CATEGORY_HATE_SPEECH",
                                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                                },
                                {
                                    category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                                },
                                {
                                    category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                                }
                            ]
                        })
                    });

                    if (response.ok) {
                        const data = await response.json();
                        if (data.candidates && data.candidates.length > 0 && data.candidates[0].content) {
                            return data.candidates[0].content.parts[0].text.trim();
                        } else {
                            throw new Error('No content generated by the API');
                        }
                    } else if (response.status === 429 && attempt < retries - 1) {
                        // Rate limited, wait and retry with exponential backoff
                        console.log(`Rate limited, retrying in ${delay}ms...`);
                        await new Promise(resolve => setTimeout(resolve, delay));
                        delay *= 2; // Exponential backoff
                        continue;
                    } else {
                        const errorData = await response.json();
                        throw new Error(errorData.error?.message || `API request failed with status ${response.status}`);
                    }
                } catch (error) {
                    if (attempt === retries - 1) {
                        throw error;
                    }
                    console.log(`Attempt ${attempt + 1} failed, retrying...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                    delay *= 1.5;
                }
            }
        }

        // Validate API key at runtime (when actually needed for content generation)
        async function validateApiKeyAtRuntime() {
            if (!geminiApiKey) {
                throw new Error('No API key available');
            }

            try {
                // Quick validation call to ensure API key works
                const response = await fetch(`https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent?key=${geminiApiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: "Test"
                            }]
                        }],
                        generationConfig: {
                            maxOutputTokens: 10
                        }
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error?.message || `API validation failed with status ${response.status}`);
                }

                // API key is valid
                return true;
            } catch (error) {
                console.error('Runtime API key validation failed:', error);
                throw error;
            }
        }

        // Generate content using Gemini API
        async function generateContentWithGemini(prompt, testName, reportName, testId) {
            if (!geminiApiKey) {
                throw new Error('API key is not available.');
            }

            // Replace placeholders in the prompt
            const processedPrompt = prompt
                .replace(/\{testName\}/g, testName || 'this test')
                .replace(/\{reportName\}/g, reportName || testName || 'this test')
                .replace(/\{testId\}/g, testId || 'unknown');

            try {
                return await makeGeminiApiCall(processedPrompt);
            } catch (error) {
                console.error('Content generation failed:', error);
                throw error;
            }
        }

        // Show API key status
        function showApiKeyStatus(message, type) {
            const statusDiv = document.getElementById('apiKeyStatus');
            statusDiv.textContent = message;
            statusDiv.className = `api-key-status ${type}`;
            statusDiv.style.display = 'block';
        }

        // Save API key to localStorage
        function saveApiKey(apiKey) {
            try {
                // Simple encoding (not encryption, just obfuscation)
                const encoded = btoa(apiKey);
                localStorage.setItem('gemini_api_key', encoded);
            } catch (error) {
                console.error('Failed to save API key:', error);
            }
        }

        // Load saved API key from localStorage
        function loadSavedApiKey() {
            try {
                const encoded = localStorage.getItem('gemini_api_key');
                if (encoded) {
                    const decoded = atob(encoded);
                    document.getElementById('geminiApiKey').value = decoded;
                    geminiApiKey = decoded;
                    // Auto-test the saved key
                    if (decoded) {
                        document.getElementById('testApiKeyButton').disabled = false;
                    }
                }
            } catch (error) {
                console.error('Failed to load saved API key:', error);
            }
        }

        // Clear saved API key (disabled for pre-configured key)
        function clearApiKey() {
            // Since we're using a pre-configured key, this function is disabled
            showStatus('Cannot clear pre-configured API key.', 'warning');
        }

        // Update character count for prompt templates
        function updateCharCount(event) {
            const textarea = event.target;
            const charCount = textarea.value.length;
            const countElement = document.getElementById(textarea.id + 'CharCount');

            if (countElement) {
                countElement.textContent = `${charCount} characters`;

                // Add warning/error classes based on length
                countElement.className = 'char-count';
                if (charCount > 500) {
                    countElement.classList.add('error');
                } else if (charCount > 400) {
                    countElement.classList.add('warning');
                }
            }
        }

        // Reset prompt template to default
        function resetPromptTemplate(templateId) {
            const textarea = document.getElementById(templateId);
            const templateKey = templateId.replace('Prompt', '').replace('Test', 'Test');

            // Map template IDs to default prompt keys
            const keyMap = {
                'aboutTestPrompt': 'aboutTest',
                'causeAbnormalPrompt': 'causeAbnormal',
                'impactAbnormalPrompt': 'impactAbnormal',
                'howToImprovePrompt': 'howToImprove'
            };

            const key = keyMap[templateId];
            if (key && defaultPrompts[key]) {
                textarea.value = defaultPrompts[key];
                updateCharCount({ target: textarea });
            }
        }

        // Function to navigate between steps
        function goToStep(stepNumber) {
            // Hide all panels
            stepPanels.forEach(panel => {
                panel.classList.remove('active');
            });

            // Update progress indicators
            stepIndicators.forEach((indicator, index) => {
                if (index + 1 < stepNumber) {
                    indicator.classList.remove('active');
                    indicator.classList.add('completed');
                } else if (index + 1 === stepNumber) {
                    indicator.classList.remove('completed');
                    indicator.classList.add('active');
                } else {
                    indicator.classList.remove('active', 'completed');
                }
            });

            // Show the current panel
            document.getElementById(`step${stepNumber}-panel`).classList.add('active');

            // If we're going to step 3, populate the tables and regenerate field mappings
            if (stepNumber === 3) {
                if (currentContentMode === 'csv') {
                    // CSV mode: Make sure we have CSV data
                    if (csvData && csvData.length > 0) {
                        // Get the column names
                        const columns = Object.keys(csvData[0]);

                        // Regenerate field mappings from CSV headers
                        generateFieldMapping(columns);

                        // Then populate the tables
                        populateTables();
                    } else {
                        showStatus('No CSV data available. Please upload a CSV file in Step 1.', 'error');
                    }
                } else {
                    // AI mode: Set up for AI content generation
                    if (jsonData && jsonData.summary_items) {
                        // Generate field mapping for AI content types
                        const aiContentTypes = ['About Test', 'Cause of Abnormal', 'Impact of Abnormal', 'How to Improve'];
                        generateFieldMapping(aiContentTypes);

                        // Populate tables with JSON test names (no CSV mapping needed)
                        populateTablesForAiMode();

                        // Update download button visibility
                        updateAiDownloadButton();
                    } else {
                        showStatus('No JSON data available. Please upload a JSON file in Step 2.', 'error');
                    }
                }
            }
        }

        // Handle CSV file selection
        function handleCsvFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            csvFileName = file.name;
            csvFileNameDisplay.textContent = `Selected: ${file.name}`;
            csvFileNameDisplay.classList.add('active');

            Papa.parse(file, {
                header: true,
                complete: function(results) {
                    csvData = results.data;
                    showStatus(`CSV file loaded successfully with ${csvData.length} rows.`, 'success');
                    updateStep1NextButton(); // Update Step 1 button state
                    updateStep2NextButton(); // Update Step 2 button state for CSV mode

                    // Process CSV data
                    processCsvData();

                    // If we're already on step 3, regenerate field mappings
                    if (document.getElementById('step3-panel').classList.contains('active')) {
                        // Get the column names
                        const columns = Object.keys(csvData[0]);

                        // Regenerate field mappings from CSV headers
                        generateFieldMapping(columns);

                        // Refresh the tables
                        populateTables();
                    }
                },
                error: function(error) {
                    csvData = null; // Clear invalid data
                    showStatus(`Error parsing CSV file: ${error.message}`, 'error');
                    updateStep1NextButton(); // Update Step 1 button state for invalid CSV
                    updateStep2NextButton(); // Update Step 2 button state for invalid CSV
                }
            });
        }

        // Handle JSON file selection
        function handleJsonFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            jsonFileName = file.name;
            jsonFileNameDisplay.textContent = `Selected: ${file.name}`;
            jsonFileNameDisplay.classList.add('active');

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    jsonData = JSON.parse(e.target.result);
                    showStatus(`JSON file loaded successfully.`, 'success');

                    // Update Step 2 Next button state based on current mode and conditions
                    updateStep2NextButton();

                    // Enable the template button now that we have JSON data
                    generateTemplateButton.disabled = false;

                    // Try to auto-map test names
                    autoMapTestNames();
                } catch (error) {
                    jsonData = null; // Clear invalid data
                    showStatus(`Error parsing JSON file: ${error.message}`, 'error');
                    updateStep2NextButton(); // Update button state for invalid JSON
                }
            };
            reader.readAsText(file);
        }

        // Handle mapping file selection
        function handleMappingFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            mappingFileNameDisplay.textContent = `Selected: ${file.name}`;
            mappingFileNameDisplay.classList.add('active');

            // Check if it's a CSV or Excel file
            const fileExtension = file.name.split('.').pop().toLowerCase();

            if (fileExtension === 'csv') {
                // Parse CSV mapping file
                Papa.parse(file, {
                    header: true,
                    complete: function(results) {
                        processMappingData(results.data);
                    },
                    error: function(error) {
                        showStatus('Error parsing mapping file: ' + error.message, 'error');
                    }
                });
            } else if (['xlsx', 'xls'].includes(fileExtension)) {
                // Parse Excel mapping file
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const data = new Uint8Array(e.target.result);
                        const workbook = XLSX.read(data, { type: 'array' });

                        // Get the first sheet
                        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
                        const jsonData = XLSX.utils.sheet_to_json(firstSheet);

                        processMappingData(jsonData);
                    } catch (error) {
                        showStatus('Error parsing Excel mapping file: ' + error.message, 'error');
                    }
                };
                reader.readAsArrayBuffer(file);
            } else {
                showStatus('Unsupported file format. Please upload a CSV or Excel file.', 'error');
            }
        }

        // Process mapping data
        function processMappingData(data) {
            if (!data || data.length === 0) {
                showStatus('No mapping data found in the file.', 'warning');
                return;
            }

            // Show loading status
            showStatus('Processing mapping data...', 'info');

            // Clear existing mappings
            testNameMappings = {};

            // Look for columns with JSON and CSV parameter names
            let jsonColumn = null;
            let csvColumn = null;

            // Try to find the column names
            const firstRow = data[0];
            const columns = Object.keys(firstRow);

            console.log('Available columns in mapping file:', columns);
            console.log('First row data:', firstRow);

            // First, try to find exact column names with more flexible matching
            const jsonColumnPatterns = [
                /json.*test.*name/i,
                /json.*parameter/i,
                /test.*name.*json/i,
                /parameter.*json/i,
                /json/i,
                /source/i
            ];

            const csvColumnPatterns = [
                /csv.*test.*name/i,
                /csv.*parameter/i,
                /test.*name.*csv/i,
                /parameter.*csv/i,
                /csv/i,
                /target/i
            ];

            // Try to match column names using patterns
            for (const column of columns) {
                for (const pattern of jsonColumnPatterns) {
                    if (pattern.test(column) && !jsonColumn) {
                        jsonColumn = column;
                        console.log(`Found JSON column: "${column}" using pattern ${pattern}`);
                        break;
                    }
                }

                for (const pattern of csvColumnPatterns) {
                    if (pattern.test(column) && !csvColumn) {
                        csvColumn = column;
                        console.log(`Found CSV column: "${column}" using pattern ${pattern}`);
                        break;
                    }
                }
            }

            // If we still couldn't find the columns, try to guess based on position
            if (!jsonColumn && !csvColumn && columns.length >= 2) {
                jsonColumn = columns[0];
                csvColumn = columns[1];
                console.log(`Using first two columns as fallback: "${jsonColumn}" for JSON and "${csvColumn}" for CSV`);
            }

            if (!jsonColumn || !csvColumn) {
                showStatus('Could not identify JSON and CSV parameter name columns in the mapping file.', 'error');
                return;
            }

            console.log(`Using mapping columns: "${jsonColumn}" for JSON and "${csvColumn}" for CSV`);

            // Debug: Show available CSV test names
            console.log('Available CSV test names:', Object.keys(csvTestData));

            // Process the mappings
            let mappedCount = 0;
            let skippedCount = 0;
            let mappingResults = [];

            // Verify that CSV parameter names exist in the CSV data
            data.forEach((row, index) => {
                const jsonTestName = row[jsonColumn];
                const csvTestName = row[csvColumn];

                if (jsonTestName) {
                    console.log(`Processing mapping ${index+1}: "${jsonTestName}" -> "${csvTestName || 'EMPTY'}"`);

                    // Store mapping result for display
                    const result = {
                        jsonTestName,
                        csvTestName: csvTestName || '',
                        status: 'pending'
                    };

                    // Handle empty CSV test name as a special case
                    if (!csvTestName || csvTestName.trim() === '' || csvTestName === '--') {
                        // Store empty mapping to clear suggestions
                        testNameMappings[jsonTestName] = '';
                        mappedCount++;
                        console.log(`✓ Empty mapping for "${jsonTestName}" - will clear suggestions`);
                        result.status = 'success';
                        result.message = 'Empty mapping - will clear suggestions';
                    }
                    // Check if the CSV parameter name exists in our CSV data
                    else if (csvTestData[csvTestName]) {
                        testNameMappings[jsonTestName] = csvTestName;
                        mappedCount++;
                        console.log(`✓ Direct match found for "${csvTestName}"`);
                        result.status = 'success';
                        result.message = 'Direct match found';
                    } else {
                        // Try to find a close match
                        const csvTestNames = Object.keys(csvTestData);
                        let bestMatch = null;
                        let bestScore = 0;

                        for (const name of csvTestNames) {
                            // Simple similarity score (percentage of characters that match)
                            const similarity = calculateSimilarity(csvTestName, name);
                            if (similarity > 0.7 && similarity > bestScore) {
                                bestScore = similarity;
                                bestMatch = name;
                            }
                        }

                        if (bestMatch) {
                            testNameMappings[jsonTestName] = bestMatch;
                            mappedCount++;
                            console.log(`✓ Mapped "${jsonTestName}" to "${bestMatch}" (similarity: ${bestScore.toFixed(2)} with "${csvTestName}")`);
                            result.status = 'success';
                            result.message = `Mapped to similar name: "${bestMatch}" (${(bestScore * 100).toFixed(0)}% match)`;
                            result.actualCsvTestName = bestMatch;
                        } else {
                            skippedCount++;
                            console.log(`✗ Skipped mapping for "${jsonTestName}" - CSV parameter name "${csvTestName}" not found`);
                            result.status = 'error';
                            result.message = 'CSV parameter name not found';
                        }
                    }

                    mappingResults.push(result);
                }
            });

            console.log(`Mapping complete: ${mappedCount} mapped, ${skippedCount} skipped`);
            console.log('Current mappings:', testNameMappings);

            // Show mapping results in a more detailed status message
            let statusHTML = `<div>Processed ${mappedCount + skippedCount} mappings: ${mappedCount} successful, ${skippedCount} skipped.</div>`;

            if (mappingResults.length > 0) {
                statusHTML += '<div style="margin-top: 10px; max-height: 150px; overflow-y: auto; font-size: 0.9rem;">';
                statusHTML += '<table style="width: 100%; border-collapse: collapse;">';
                statusHTML += '<tr style="background-color: #f8f9fa;"><th style="text-align: left; padding: 4px;">JSON Test Name</th><th style="text-align: left; padding: 4px;">CSV Test Name</th><th style="text-align: left; padding: 4px;">Status</th></tr>';

                mappingResults.forEach(result => {
                    const statusColor = result.status === 'success' ? '#4ade80' : '#f43f5e';
                    const statusIcon = result.status === 'success' ? '✓' : '✗';

                    statusHTML += `<tr>`;
                    statusHTML += `<td style="padding: 4px; border-top: 1px solid #dee2e6;">${result.jsonTestName}</td>`;
                    statusHTML += `<td style="padding: 4px; border-top: 1px solid #dee2e6;">${result.actualCsvTestName || result.csvTestName}</td>`;
                    statusHTML += `<td style="padding: 4px; border-top: 1px solid #dee2e6;"><span style="color: ${statusColor};">${statusIcon} ${result.message}</span></td>`;
                    statusHTML += `</tr>`;
                });

                statusHTML += '</table>';
                statusHTML += '</div>';
            }

            if (skippedCount > 0) {
                showStatus(statusHTML, 'warning', true);
            } else {
                showStatus(statusHTML, 'success', true);
            }

            // If we're on step 3, refresh the tables
            if (document.getElementById('step3-panel').classList.contains('active')) {
                populateTables();
            }
        }

        // Calculate similarity between two strings (0-1)
        function calculateSimilarity(str1, str2) {
            // Convert to lowercase for case-insensitive comparison
            const s1 = str1.toLowerCase();
            const s2 = str2.toLowerCase();

            // If strings are identical, return 1
            if (s1 === s2) return 1;

            // If one string contains the other, return a high score
            if (s1.includes(s2) || s2.includes(s1)) {
                return 0.9;
            }

            // Calculate Levenshtein distance
            const track = Array(s2.length + 1).fill(null).map(() =>
                Array(s1.length + 1).fill(null));

            for (let i = 0; i <= s1.length; i++) {
                track[0][i] = i;
            }

            for (let j = 0; j <= s2.length; j++) {
                track[j][0] = j;
            }

            for (let j = 1; j <= s2.length; j++) {
                for (let i = 1; i <= s1.length; i++) {
                    const indicator = s1[i - 1] === s2[j - 1] ? 0 : 1;
                    track[j][i] = Math.min(
                        track[j][i - 1] + 1, // deletion
                        track[j - 1][i] + 1, // insertion
                        track[j - 1][i - 1] + indicator // substitution
                    );
                }
            }

            // Calculate similarity as 1 - normalized distance
            const maxLength = Math.max(s1.length, s2.length);
            return 1 - (track[s2.length][s1.length] / maxLength);
        }

        // Generate mapping template
        function generateMappingTemplate() {
            if (!jsonData || !jsonData.summary_items) {
                showStatus('Please upload a JSON file first to generate a mapping template.', 'warning');
                return;
            }

            // Create data for the template
            const templateData = [];

            // Get available CSV test names for the dropdown list
            const availableCsvTestNames = Object.keys(csvTestData);

            // Add instructions row
            templateData.push({
                'JSON Test Name': '*** INSTRUCTIONS ***',
                'CSV Test Name': 'Fill in the CSV Test Name column with the corresponding CSV parameter name',
                'Is Special': 'Do not modify this column'
            });

            templateData.push({
                'JSON Test Name': '*** EXAMPLE ***',
                'CSV Test Name': availableCsvTestNames.length > 0 ? availableCsvTestNames[0] : 'Example CSV Test Name',
                'Is Special': 'No'
            });

            // Add separator row
            templateData.push({
                'JSON Test Name': '--------------------',
                'CSV Test Name': '--------------------',
                'Is Special': '----'
            });

            // Add all JSON test names
            jsonData.summary_items.forEach(item => {
                if (item.testName) {
                    templateData.push({
                        'JSON Test Name': item.testName,
                        'CSV Test Name': testNameMappings[item.testName] || '',
                        'Is Special': isSpecialTestName(item.testName) ? 'Yes' : 'No'
                    });
                }
            });

            // Add available CSV test names as a reference
            templateData.push({
                'JSON Test Name': '--------------------',
                'CSV Test Name': '--------------------',
                'Is Special': '----'
            });

            templateData.push({
                'JSON Test Name': '*** AVAILABLE CSV TEST NAMES ***',
                'CSV Test Name': 'Copy and paste these names into the CSV Test Name column above',
                'Is Special': ''
            });

            // Add up to 20 CSV test names as reference
            const maxCsvNames = Math.min(availableCsvTestNames.length, 20);
            for (let i = 0; i < maxCsvNames; i++) {
                templateData.push({
                    'JSON Test Name': `CSV Test ${i+1}`,
                    'CSV Test Name': availableCsvTestNames[i],
                    'Is Special': ''
                });
            }

            if (availableCsvTestNames.length > 20) {
                templateData.push({
                    'JSON Test Name': '... and more',
                    'CSV Test Name': `${availableCsvTestNames.length - 20} more CSV test names available`,
                    'Is Special': ''
                });
            }

            // Create workbook
            const workbook = XLSX.utils.book_new();
            const worksheet = XLSX.utils.json_to_sheet(templateData);

            // Add some column width information
            const colWidths = [
                { wch: 40 }, // JSON Test Name
                { wch: 60 }, // CSV Test Name
                { wch: 10 }  // Is Special
            ];

            worksheet['!cols'] = colWidths;

            // Add some formatting
            const range = XLSX.utils.decode_range(worksheet['!ref']);

            // Create a new style object for the header rows
            const headerStyle = {
                font: { bold: true, color: { rgb: "FF0000" } },
                fill: { fgColor: { rgb: "FFEEEE" } }
            };

            // Apply styles to the header rows
            for (let R = 0; R <= 2; R++) {
                for (let C = range.s.c; C <= range.e.c; C++) {
                    const cell_address = { r: R, c: C };
                    const cell_ref = XLSX.utils.encode_cell(cell_address);
                    if (!worksheet[cell_ref]) continue;
                    worksheet[cell_ref].s = headerStyle;
                }
            }

            // Add the worksheet to the workbook
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Test Mapping');

            // Get the selected format
            const format = templateFormatSelect.value;

            // Download the file
            if (format === 'csv') {
                // Convert to CSV and download
                const csv = XLSX.utils.sheet_to_csv(worksheet);
                const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
                const url = URL.createObjectURL(blob);

                const a = document.createElement('a');
                a.href = url;
                a.download = 'test_mapping_template.csv';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            } else {
                // Download as Excel
                XLSX.writeFile(workbook, 'test_mapping_template.xlsx');
            }

            showStatus('Mapping template generated and downloaded. Please fill in the CSV Test Name column and upload it back.', 'success');
        }

        // Process CSV data
        function processCsvData() {
            if (!csvData || csvData.length === 0) {
                console.log('No CSV data to process');
                return;
            }

            // Get the column names
            const columns = Object.keys(csvData[0]);

            console.log('CSV data sample:', csvData[0]);
            console.log('CSV columns:', columns);

            // Try to identify the test name column
            let testNameColumn = columns[0]; // Default to first column

            // Look for columns that might contain test names
            for (const column of columns) {
                const lowerColumn = column.toLowerCase();
                if (lowerColumn.includes('test') && lowerColumn.includes('name') ||
                    lowerColumn.includes('parameter') ||
                    lowerColumn.includes('dic. name')) {
                    testNameColumn = column;
                    console.log(`Using "${column}" as the test name column`);
                    break;
                }
            }

            // Store CSV data by test name
            csvData.forEach((row, index) => {
                const testName = row[testNameColumn];
                if (testName) {
                    // Store with original name
                    csvTestData[testName] = row;

                    // Also store with trimmed name for better matching
                    const trimmedName = testName.trim();
                    if (trimmedName !== testName) {
                        csvTestData[trimmedName] = row;
                    }

                    // If the test name contains "Dic. Name | Checked", also store with the part after "|"
                    if (testName.includes('|')) {
                        const parts = testName.split('|');
                        if (parts.length > 1) {
                            const simplifiedName = parts[1].trim();
                            csvTestData[simplifiedName] = row;
                            console.log(`Added alternative mapping: "${testName}" -> "${simplifiedName}"`);
                        }
                    }
                } else {
                    console.log(`Row ${index + 1} has no test name in column "${testNameColumn}"`);
                }
            });

            console.log(`Processed ${Object.keys(csvTestData).length} unique test names from CSV`);

            // Store the CSV field mapping for later use
            generateFieldMapping(columns);
        }

        // Field mapping between CSV headers and JSON field IDs
        const fieldMappings = {
            // Default mappings
            'About Parameter': { id: 'about-test', title: 'About Test' },
            'Cause of abnormal result': { id: 'cause-of-abnormal-result', title: 'Cause' },
            'Impact of abnormal result': { id: 'impact', title: 'Impact' },
            'How to improve': { id: 'how-to-improve', title: 'How to Improve' }
        };

        // Debounce function to limit how often a function can be called
        function debounce(func, wait) {
            let timeout;
            return function(...args) {
                const context = this;
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(context, args), wait);
            };
        }

        // Function to update all preview content
        function updateAllPreviews() {
            // Show loading indicator
            document.querySelectorAll('.preview-content').forEach(preview => {
                preview.innerHTML = '<div class="loading-fields"><span class="material-icons-round loading-spinner">sync</span> Updating preview...</div>';
            });

            // Use setTimeout to prevent UI freeze
            setTimeout(() => {
                document.querySelectorAll('.preview-content').forEach(preview => {
                    const index = preview.id.split('-')[1];
                    const test = mappedTests.find(t => t.index == index);
                    if (test) {
                        const csvRow = csvTestData[test.csvTestName];
                        let previewContent = '';

                        document.querySelectorAll('.field-checkbox:checked').forEach(cb => {
                            const csvColumn = cb.value; // The value is now the exact column name

                            if (csvRow[csvColumn]) {
                                previewContent += `${csvColumn}: ${csvRow[csvColumn]}\n`;
                            }
                        });

                        preview.textContent = previewContent || 'No fields selected for preview';
                    }
                });
            }, 10);
        }

        // Create a debounced version of the update function
        const debouncedUpdatePreviews = debounce(updateAllPreviews, 300);

        // Generate field mapping from CSV headers
        function generateFieldMapping(columns) {
            // Clear existing mappings
            const fieldsCheckboxGroup = document.getElementById('fieldsCheckboxGroup');
            fieldsCheckboxGroup.innerHTML = '';

            // Track if we found any mappable fields
            let foundMappableFields = false;

            // Debug log
            console.log('Generating field mapping from columns:', columns);

            // Try to identify the test name column
            let testNameColumn = null;

            // Look for columns that might contain test names
            for (const column of columns) {
                const lowerColumn = column.toLowerCase();
                if (lowerColumn.includes('test') && lowerColumn.includes('name') ||
                    lowerColumn.includes('parameter') ||
                    lowerColumn.includes('dic. name') ||
                    lowerColumn === 'name') {
                    testNameColumn = column;
                    console.log(`Identified "${column}" as the test name column`);
                    break;
                }
            }

            // If no test name column was found, assume the first column is the test name
            if (!testNameColumn && columns.length > 0) {
                testNameColumn = columns[0];
                console.log(`Using first column "${testNameColumn}" as the test name column`);
            }

            // Process all columns except the test name column
            for (let i = 0; i < columns.length; i++) {
                const column = columns[i];

                // Skip empty columns and the test name column
                if (!column.trim() || column === testNameColumn) continue;

                // Create checkbox
                const label = document.createElement('label');
                label.className = 'checkbox-label';

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.className = 'field-checkbox';
                checkbox.value = column; // Use the exact column name as the value
                checkbox.dataset.csvColumn = column;
                checkbox.checked = true;

                // Add change event listener with debounce
                checkbox.addEventListener('change', debouncedUpdatePreviews);

                const span = document.createElement('span');
                span.textContent = column; // Use the original column name as the label

                label.appendChild(checkbox);
                label.appendChild(span);
                fieldsCheckboxGroup.appendChild(label);

                foundMappableFields = true;
            }

            // If no mappable fields were found, show a message
            if (!foundMappableFields) {
                fieldsCheckboxGroup.innerHTML = `
                    <div class="status status-warning" style="margin: 0;">
                        <span class="material-icons-round">warning</span>
                        <div>No mappable fields found in the CSV. Please ensure your CSV contains multiple columns.</div>
                    </div>
                `;
            }
        }

        // Auto-map test names
        function autoMapTestNames() {
            if (!jsonData || !jsonData.summary_items || !csvTestData) return;

            // Clear existing mappings
            testNameMappings = {};

            // Extract test names from JSON
            const jsonTestNames = [];
            jsonData.summary_items.forEach(item => {
                if (item.testName) {
                    jsonTestNames.push(item.testName);
                }
            });

            console.log(`Found ${jsonTestNames.length} test names in JSON`);
            console.log(`Found ${Object.keys(csvTestData).length} test names in CSV`);

            // Try to auto-map based on exact matches and similarity
            let exactMatchCount = 0;
            let similarMatchCount = 0;
            let unmatchedCount = 0;

            jsonTestNames.forEach(jsonTestName => {
                // Skip special test names (comments, notes, etc.)
                if (isSpecialTestName(jsonTestName)) {
                    console.log(`Skipping special test name: "${jsonTestName}"`);

                    // Special handling for test names with "Blank" in them
                    if (jsonTestName.includes("Blank")) {
                        console.log(`Setting empty mapping for test with "Blank" in name: ${jsonTestName}`);
                        testNameMappings[jsonTestName] = ''; // Set empty mapping to clear suggestions
                    }

                    return;
                }

                // Check if there's an exact match in CSV data
                if (csvTestData[jsonTestName]) {
                    testNameMappings[jsonTestName] = jsonTestName;
                    exactMatchCount++;
                    console.log(`✓ Exact match found for "${jsonTestName}"`);
                    return;
                }

                // Check for matches with "Dic. Name | Checked" prefix or other patterns
                let found = false;
                Object.keys(csvTestData).forEach(csvTestName => {
                    if (csvTestName.includes(jsonTestName) ||
                        (csvTestName.toLowerCase().includes('dic. name') && csvTestName.includes(jsonTestName)) ||
                        (jsonTestName.includes(csvTestName) && csvTestName.length > 3)) {
                        testNameMappings[jsonTestName] = csvTestName;
                        similarMatchCount++;
                        found = true;
                        console.log(`✓ Pattern match found: "${jsonTestName}" -> "${csvTestName}"`);
                        return;
                    }
                });

                // If no match found yet, try similarity matching
                if (!found) {
                    // Find the best match based on similarity
                    let bestMatch = null;
                    let bestScore = 0;

                    Object.keys(csvTestData).forEach(csvTestName => {
                        const similarity = calculateSimilarity(jsonTestName, csvTestName);
                        if (similarity > 0.7 && similarity > bestScore) {
                            bestScore = similarity;
                            bestMatch = csvTestName;
                        }
                    });

                    if (bestMatch) {
                        testNameMappings[jsonTestName] = bestMatch;
                        similarMatchCount++;
                        console.log(`✓ Similarity match found: "${jsonTestName}" -> "${bestMatch}" (${(bestScore * 100).toFixed(0)}%)`);
                    } else {
                        unmatchedCount++;
                        console.log(`✗ No match found for "${jsonTestName}"`);
                    }
                }
            });

            const mappedCount = exactMatchCount + similarMatchCount;
            if (mappedCount > 0) {
                showStatus(`Auto-mapped ${mappedCount} test names (${exactMatchCount} exact matches, ${similarMatchCount} similar matches). ${unmatchedCount} tests could not be mapped.`, 'success');
                console.log('Final mappings:', testNameMappings);
            } else {
                showStatus(`Could not auto-map any test names. Please upload a mapping file.`, 'warning');
            }
        }

        // Global variables for test data
        let mappedTests = [];
        let unmappedTests = [];

        // Populate tables with mapped and unmapped tests
        function populateTables() {
            if (!jsonData || !jsonData.summary_items) {
                showStatus('No JSON data available for preview.', 'error');
                return;
            }

            // Show loading status
            showStatus('Processing test data...', 'info');

            // Use setTimeout to prevent UI freeze
            setTimeout(() => {
                // Clear the tables
                mappedTableBody.innerHTML = '';
                unmappedTableBody.innerHTML = '';

                // Reset test arrays
                mappedTests = [];
                unmappedTests = [];

                // Categorize tests as mapped or unmapped
                jsonData.summary_items.forEach((item, index) => {
                    if (!item.testName) return;

                    const jsonTestName = item.testName;
                    const csvTestName = testNameMappings[jsonTestName];

                    if (csvTestName && csvTestData[csvTestName]) {
                        mappedTests.push({
                            index,
                            item,
                            jsonTestName,
                            csvTestName,
                            isSpecial: isSpecialTestName(jsonTestName)
                        });
                    } else {
                        unmappedTests.push({
                            index,
                            item,
                            jsonTestName,
                            isSpecial: isSpecialTestName(jsonTestName)
                        });
                    }
                });

                // Update count badges
                mappedCountBadge.textContent = mappedTests.length;
                unmappedCountBadge.textContent = unmappedTests.length;

                // Use document fragment for better performance
                const mappedFragment = document.createDocumentFragment();
                const unmappedFragment = document.createDocumentFragment();

                // Process in batches for better performance
                const processMappedBatch = (startIndex, batchSize) => {
                    const endIndex = Math.min(startIndex + batchSize, mappedTests.length);

                    for (let i = startIndex; i < endIndex; i++) {
                        const test = mappedTests[i];
                        const row = document.createElement('tr');
                        row.className = 'test-row';

                        if (test.isSpecial) {
                            row.classList.add('special-test');
                        }

                        // Checkbox cell
                        const checkboxCell = document.createElement('td');
                        checkboxCell.className = 'checkbox-cell';

                        const checkboxLabel = document.createElement('label');
                        checkboxLabel.className = 'checkbox-label';

                        const checkbox = document.createElement('input');
                        checkbox.type = 'checkbox';
                        checkbox.checked = !test.isSpecial;
                        checkbox.dataset.index = test.index;

                        const checkboxSpan = document.createElement('span');

                        checkboxLabel.appendChild(checkbox);
                        checkboxLabel.appendChild(checkboxSpan);
                        checkboxCell.appendChild(checkboxLabel);

                        // JSON test name cell
                        const jsonTestNameCell = document.createElement('td');
                        jsonTestNameCell.textContent = test.jsonTestName;

                        // CSV test name cell
                        const csvTestNameCell = document.createElement('td');
                        csvTestNameCell.textContent = test.csvTestName;

                        // Content preview cell
                        const contentPreviewCell = document.createElement('td');
                        const previewDiv = document.createElement('div');
                        previewDiv.className = 'preview-content';
                        previewDiv.id = `preview-${test.index}`;

                        // Add placeholder text - we'll update all previews at once later
                        previewDiv.textContent = 'Loading preview...';

                        contentPreviewCell.appendChild(previewDiv);

                        // Add cells to row
                        row.appendChild(checkboxCell);
                        row.appendChild(jsonTestNameCell);
                        row.appendChild(csvTestNameCell);
                        row.appendChild(contentPreviewCell);

                        // Add row to fragment
                        mappedFragment.appendChild(row);
                    }

                    // If we have more items to process, schedule the next batch
                    if (endIndex < mappedTests.length) {
                        setTimeout(() => {
                            processMappedBatch(endIndex, batchSize);
                        }, 0);
                    } else {
                        // All mapped tests processed, append fragment to table
                        mappedTableBody.appendChild(mappedFragment);

                        // Update all previews at once
                        updateAllPreviews();

                        // Process unmapped tests
                        processUnmappedBatch(0, 50);
                    }
                };

                const processUnmappedBatch = (startIndex, batchSize) => {
                    const endIndex = Math.min(startIndex + batchSize, unmappedTests.length);

                    for (let i = startIndex; i < endIndex; i++) {
                        const test = unmappedTests[i];
                        const row = document.createElement('tr');
                        row.className = 'test-row';

                        if (test.isSpecial) {
                            row.classList.add('special-test');
                        }

                        // JSON test name cell
                        const jsonTestNameCell = document.createElement('td');
                        jsonTestNameCell.textContent = test.jsonTestName;

                        // CSV test name select cell
                        const csvTestSelectCell = document.createElement('td');
                        const selectElement = document.createElement('select');
                        selectElement.className = 'csv-test-select';
                        selectElement.dataset.jsonTestName = test.jsonTestName;
                        selectElement.dataset.index = test.index;

                        // Add empty option
                        const emptyOption = document.createElement('option');
                        emptyOption.value = '';
                        emptyOption.textContent = '-- Select CSV Test --';
                        selectElement.appendChild(emptyOption);

                        // Add options for each CSV test
                        Object.keys(csvTestData).forEach(name => {
                            const option = document.createElement('option');
                            option.value = name;
                            option.textContent = name;
                            selectElement.appendChild(option);
                        });

                        csvTestSelectCell.appendChild(selectElement);

                        // Add cells to row
                        row.appendChild(jsonTestNameCell);
                        row.appendChild(csvTestSelectCell);

                        // Add row to fragment
                        unmappedFragment.appendChild(row);

                        // Add event listener for CSV test select
                        selectElement.addEventListener('change', function() {
                            const selectedCsvTest = this.value;
                            if (selectedCsvTest) {
                                testNameMappings[test.jsonTestName] = selectedCsvTest;
                                showStatus(`Mapped "${test.jsonTestName}" to "${selectedCsvTest}". Refreshing tables...`, 'success');

                                // Refresh tables after a short delay
                                setTimeout(() => {
                                    populateTables();
                                }, 500);
                            }
                        });
                    }

                    // If we have more items to process, schedule the next batch
                    if (endIndex < unmappedTests.length) {
                        setTimeout(() => {
                            processUnmappedBatch(endIndex, batchSize);
                        }, 0);
                    } else {
                        // All unmapped tests processed, append fragment to table
                        unmappedTableBody.appendChild(unmappedFragment);

                        // Show status
                        showStatus(`Found ${mappedTests.length} mapped tests and ${unmappedTests.length} unmapped tests.`, 'info');
                    }
                };

                // Start processing in batches (50 items per batch)
                processMappedBatch(0, 50);
            }, 10);
        }

        // Populate tables for AI mode (no CSV mapping needed)
        function populateTablesForAiMode() {
            if (!jsonData || !jsonData.summary_items) {
                showStatus('No JSON data available for preview.', 'error');
                return;
            }

            // Show loading status
            showStatus('Setting up AI content generation...', 'info');

            // Clear the tables
            mappedTableBody.innerHTML = '';
            unmappedTableBody.innerHTML = '';

            // Reset test arrays
            mappedTests = [];
            unmappedTests = [];

            // In AI mode, all tests are considered "mapped" since we'll generate content for them
            jsonData.summary_items.forEach((item, index) => {
                if (!item.testName) return;

                const jsonTestName = item.testName;
                const isSpecial = isSpecialTestName(jsonTestName);

                // Add all tests to mapped (they'll get AI-generated content)
                mappedTests.push({
                    index,
                    item,
                    jsonTestName,
                    csvTestName: 'AI Generated Content',
                    isSpecial: isSpecial
                });
            });

            // Update count badges
            mappedCountBadge.textContent = mappedTests.length;
            unmappedCountBadge.textContent = 0;

            // Create table rows for mapped tests
            const mappedFragment = document.createDocumentFragment();

            mappedTests.forEach(test => {
                const row = document.createElement('tr');
                row.className = 'test-row';

                if (test.isSpecial) {
                    row.classList.add('special-test');
                }

                // Checkbox cell
                const checkboxCell = document.createElement('td');
                checkboxCell.className = 'checkbox-cell';

                const checkboxLabel = document.createElement('label');
                checkboxLabel.className = 'checkbox-label';

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.checked = !test.isSpecial; // Don't select special tests by default
                checkbox.dataset.index = test.index;

                const checkboxSpan = document.createElement('span');

                checkboxLabel.appendChild(checkbox);
                checkboxLabel.appendChild(checkboxSpan);
                checkboxCell.appendChild(checkboxLabel);

                // JSON test name cell
                const jsonTestNameCell = document.createElement('td');
                jsonTestNameCell.textContent = test.jsonTestName;

                // Content source cell (instead of CSV test name)
                const contentSourceCell = document.createElement('td');
                contentSourceCell.innerHTML = '<span style="color: var(--primary-color); font-weight: 500;">AI Generated</span>';

                // Content preview cell
                const contentPreviewCell = document.createElement('td');
                const previewDiv = document.createElement('div');
                previewDiv.className = 'preview-content';
                previewDiv.id = `preview-${test.index}`;

                // Show what content will be generated
                const selectedTypes = [];
                if (document.getElementById('generateAboutTest').checked) selectedTypes.push('About Test');
                if (document.getElementById('generateCauseAbnormal').checked) selectedTypes.push('Cause of Abnormal');
                if (document.getElementById('generateImpactAbnormal').checked) selectedTypes.push('Impact of Abnormal');
                if (document.getElementById('generateHowToImprove').checked) selectedTypes.push('How to Improve');

                previewDiv.innerHTML = `<em>Will generate: ${selectedTypes.join(', ')}</em>`;

                contentPreviewCell.appendChild(previewDiv);

                // Add cells to row
                row.appendChild(checkboxCell);
                row.appendChild(jsonTestNameCell);
                row.appendChild(contentSourceCell);
                row.appendChild(contentPreviewCell);

                // Add row to fragment
                mappedFragment.appendChild(row);
            });

            // Append all rows to table
            mappedTableBody.appendChild(mappedFragment);

            // Show status
            showStatus(`Ready to generate AI content. Select the tests you want to process using the checkboxes, then click "Update JSON" to start generation.`, 'success');
        }

        // Check if a test name is a special case (comments, notes, blank, etc.)
        function isSpecialTestName(testName) {
            if (!testName) return false;

            // Check for "Blank" in the test name
            if (testName.includes("Blank")) {
                return true;
            }

            const specialPatterns = [
                /comments\s*:/i,
                /comment/i,
                /note/i,
                /notes/i,
                /nbsp/i,
                /interpretation/i,
                /interpretations\s*:/i
            ];

            return specialPatterns.some(pattern => pattern.test(testName));
        }

        // Search tests
        function searchTests(searchTerm, tableBody) {
            searchTerm = searchTerm.toLowerCase();
            const rows = tableBody.querySelectorAll('tr');

            rows.forEach(row => {
                const jsonTestName = row.cells[0].textContent.toLowerCase();
                let csvTestName = '';

                // Check if this is a mapped or unmapped table
                if (row.cells.length > 2) {
                    // Mapped table
                    csvTestName = row.cells[2].textContent.toLowerCase();
                }

                if (jsonTestName.includes(searchTerm) || csvTestName.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // Clear search
        function clearSearch(searchInput, tableBody) {
            searchInput.value = '';
            const rows = tableBody.querySelectorAll('tr');
            rows.forEach(row => {
                row.style.display = '';
            });
        }

        // Toggle select all mapped checkboxes
        function toggleSelectAllMapped() {
            const checkboxes = mappedTableBody.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAllMappedCheckbox.checked;
            });
        }



        // Download all data
        function downloadAllData() {
            if (!jsonData || !jsonData.summary_items) {
                showStatus('No data available to download.', 'error');
                return;
            }

            // Collect all data
            const mappedData = [];
            const unmappedData = [];

            jsonData.summary_items.forEach(item => {
                if (!item.testName) return;

                const jsonTestName = item.testName;
                const csvTestName = testNameMappings[jsonTestName];

                if (csvTestName && csvTestData[csvTestName]) {
                    const csvRow = csvTestData[csvTestName];

                    mappedData.push({
                        'JSON Parameter Name': jsonTestName,
                        'CSV Parameter Name': csvTestName,
                        'About Parameter': csvRow['About Parameter'] || '',
                        'Cause': csvRow['Cause of abnormal result'] || '',
                        'Impact': csvRow['Impact of abnormal result'] || '',
                        'How to Improve': csvRow['How to improve'] || '',
                        'Is Special': isSpecialTestName(jsonTestName) ? 'Yes' : 'No'
                    });
                } else {
                    unmappedData.push({
                        'JSON Parameter Name': jsonTestName,
                        'CSV Parameter Name': '',
                        'Is Special': isSpecialTestName(jsonTestName) ? 'Yes' : 'No'
                    });
                }
            });

            // Create workbook
            const workbook = XLSX.utils.book_new();

            // Add mapped data sheet
            const mappedWorksheet = XLSX.utils.json_to_sheet(mappedData);
            XLSX.utils.book_append_sheet(workbook, mappedWorksheet, 'Mapped Parameters');

            // Add unmapped data sheet
            const unmappedWorksheet = XLSX.utils.json_to_sheet(unmappedData);
            XLSX.utils.book_append_sheet(workbook, unmappedWorksheet, 'Unmapped Parameters');

            // Download
            XLSX.writeFile(workbook, 'all_data.xlsx');
            showStatus('All data downloaded successfully.', 'success');
        }

        /**
         * Update JSON with selected data
         * This is an improved version that handles various edge cases
         */
        function updateJson() {
            if (!jsonData || !jsonData.summary_items) {
                showStatus('No JSON data to update.', 'error');
                return;
            }

            // Check if we're in AI mode
            if (currentContentMode === 'ai') {
                generateAiContentForSelectedTests();
                return;
            }

            // Show progress
            updateProgress.style.display = 'block';
            updateButton.style.display = 'none';

            // Clone the JSON data
            updatedJsonData = JSON.parse(JSON.stringify(jsonData));

            // DIRECT CHECK: First pass to handle all test names with "Blank"
            updatedJsonData.summary_items.forEach(item => {
                if (item.testName && item.testName.includes("Blank")) {
                    console.log(`DIRECT CHECK: Clearing suggestions for test with "Blank" in name: ${item.testName}`);
                    item.suggestions = [];
                }
            });

            // Get selected fields to update
            const fieldsToUpdate = [];
            document.querySelectorAll('.field-checkbox:checked').forEach(checkbox => {
                fieldsToUpdate.push(checkbox.value);
            });

            // Get selected tests from mapped table
            const selectedTests = [];
            mappedTableBody.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
                selectedTests.push(parseInt(checkbox.dataset.index));
            });

            // Count for progress
            let processedCount = 0;
            const totalCount = selectedTests.length;

            if (totalCount === 0) {
                showStatus('No parameters selected for update. Please select at least one parameter in the Mapped Parameters tab.', 'error');
                updateProgress.style.display = 'none';
                updateButton.style.display = 'block';
                return;
            }

            console.log(`Updating ${totalCount} tests with ${fieldsToUpdate.length} fields each`);
            console.log(`Fields to update:`, fieldsToUpdate);

            // Get the selected update mode
            const updateMode = document.querySelector('input[name="jsonUpdateMode"]:checked').value;
            console.log(`Update mode: ${updateMode}`);

            // First, handle unmapped tests if in replace mode
            if (updateMode === 'replace') {
                // Get all unmapped test indices
                const unmappedIndices = [];
                unmappedTests.forEach(test => {
                    unmappedIndices.push(test.index);
                });

                console.log(`Found ${unmappedIndices.length} unmapped tests to process in replace mode`);

                // Clear suggestions for all unmapped tests
                unmappedIndices.forEach(index => {
                    const item = updatedJsonData.summary_items[index];
                    const jsonTestName = item.testName;

                    console.log(`Clearing suggestions for unmapped test: ${jsonTestName}`);

                    // Set empty suggestions array for unmapped tests
                    item.suggestions = [];
                });

                // Also handle tests with empty mappings, tests not in the mapping file, or tests with "Blank" in the name
                updatedJsonData.summary_items.forEach((item, index) => {
                    const jsonTestName = item.testName;

                    // Special case: If test name contains "Blank", always clear suggestions
                    if (jsonTestName && jsonTestName.includes("Blank")) {
                        console.log(`Clearing suggestions for test with "Blank" in name: ${jsonTestName}`);
                        item.suggestions = [];
                        return; // Skip further processing for this item
                    }

                    // Check if this test is in the mapping file
                    if (jsonTestName in testNameMappings) {
                        const csvTestName = testNameMappings[jsonTestName];

                        // Check if this test has an empty mapping or no CSV data
                        if (csvTestName === "" || (csvTestName && !csvTestData[csvTestName])) {
                            console.log(`Clearing suggestions for test with empty mapping: ${jsonTestName}`);
                            item.suggestions = [];
                        }
                    } else {
                        // This test is not in the mapping file at all
                        // In replace mode, we should also clear these
                        console.log(`Clearing suggestions for test not in mapping file: ${jsonTestName}`);
                        item.suggestions = [];
                    }
                });
            }

            // Process each selected test
            selectedTests.forEach(index => {
                const item = updatedJsonData.summary_items[index];
                const jsonTestName = item.testName;
                const csvTestName = testNameMappings[jsonTestName];

                console.log(`Processing test: ${jsonTestName} -> ${csvTestName}`);

                if (csvTestName && csvTestData[csvTestName]) {
                    const csvRow = csvTestData[csvTestName];

                    // Fix existing suggestions first (trim IDs, fix titles)
                    if (item.suggestions) {
                        item.suggestions.forEach(suggestion => {
                            // Fix ID: trim spaces
                            if (suggestion.id) {
                                suggestion.id = suggestion.id.trim();
                            }

                            // Fix title: use the ID if title is in a different language or empty
                            if (!suggestion.title || suggestion.title.trim() === '' ||
                                /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]/.test(suggestion.title)) { // Arabic character range
                                suggestion.title = suggestion.id;
                            }
                        });
                    }

                    // If replace mode, clear all existing suggestions
                    if (updateMode === 'replace') {
                        console.log(`Clearing all existing suggestions for ${jsonTestName}`);
                        item.suggestions = [];
                    } else if (!item.suggestions) {
                        // Ensure suggestions array exists
                        item.suggestions = [];
                    }

                    // Process each selected field
                    for (const field of fieldsToUpdate) {
                        const csvColumn = field;
                        const content = csvRow[csvColumn];

                        // Skip empty content
                        if (!content || content.trim() === '') {
                            console.log(`No content for column ${csvColumn}, skipping`);
                            continue;
                        }

                        // Create clean ID from field name
                        const fieldId = csvColumn.trim();

                        console.log(`Processing field: ${fieldId}, content: ${content.substring(0, 30)}...`);

                        // Check if we should update existing fields or add new ones
                        if (updateMode === 'update') {
                            // Only update existing fields
                            const existingSuggestion = item.suggestions.find(s => s.id === fieldId);
                            if (existingSuggestion) {
                                console.log(`Updating existing suggestion: id=${fieldId}`);
                                existingSuggestion.content = content;
                                existingSuggestion.title = fieldId; // Use field ID as title for consistency
                            } else {
                                console.log(`Suggestion with id=${fieldId} doesn't exist, skipping (update mode)`);
                            }
                        } else {
                            // Add or replace mode
                            const existingSuggestion = item.suggestions.find(s => s.id === fieldId);

                            if (existingSuggestion) {
                                // Update existing suggestion
                                console.log(`Updating existing suggestion: id=${fieldId}`);
                                existingSuggestion.content = content;
                                existingSuggestion.title = fieldId; // Use field ID as title for consistency
                            } else {
                                // Create a new suggestion object
                                console.log(`Adding new suggestion: id=${fieldId}`);
                                const suggestion = {
                                    id: fieldId,
                                    title: fieldId, // Use field ID as title for consistency
                                    content: content
                                };

                                // Add the suggestion to the item
                                item.suggestions.push(suggestion);
                            }
                        }
                    }

                    console.log(`Test ${jsonTestName} now has ${item.suggestions.length} suggestions`);
                    console.log(`Suggestions:`, JSON.stringify(item.suggestions, null, 2));
                } else {
                    console.log(`No CSV data found for test ${jsonTestName}, skipping`);
                }

                // Update progress
                processedCount++;
                const progress = Math.round((processedCount / totalCount) * 100);
                progressBar.style.width = `${progress}%`;
                progressText.textContent = `${progress}% (${processedCount}/${totalCount})`;

                // If all processed, show download button
                if (processedCount === totalCount) {
                    setTimeout(() => {
                        updateProgress.style.display = 'none';
                        updateComplete.style.display = 'block';
                        downloadButton.style.display = 'block';

                        // Count tests with empty mappings, tests not in mapping file, and tests with "Blank" in name
                        let emptyMappingCount = 0;
                        let notInMappingCount = 0;
                        let blankTestCount = 0;
                        if (updateMode === 'replace') {
                            updatedJsonData.summary_items.forEach(item => {
                                const jsonTestName = item.testName;

                                // Check for "Blank" in test name
                                if (jsonTestName && jsonTestName.includes("Blank")) {
                                    blankTestCount++;
                                    return; // Skip further counting for this item
                                }

                                if (jsonTestName in testNameMappings) {
                                    const csvTestName = testNameMappings[jsonTestName];
                                    if (csvTestName === "" || (csvTestName && !csvTestData[csvTestName])) {
                                        emptyMappingCount++;
                                    }
                                } else {
                                    // Test not in mapping file at all
                                    notInMappingCount++;
                                }
                            });
                        }

                        // Update summary with unmapped tests info
                        const unmappedCount = updateMode === 'replace' ? unmappedTests.length : 0;
                        changeSummary.innerHTML = `
                            <li>Updated ${totalCount} mapped parameter entries</li>
                            ${unmappedCount > 0 ? `<li>Cleared suggestions for ${unmappedCount} unmapped parameters</li>` : ''}
                            ${emptyMappingCount > 0 ? `<li>Cleared suggestions for ${emptyMappingCount} parameters with empty mappings</li>` : ''}
                            ${blankTestCount > 0 ? `<li>Cleared suggestions for ${blankTestCount} parameters with "Blank" in name</li>` : ''}
                            ${notInMappingCount > 0 ? `<li>Cleared suggestions for ${notInMappingCount} parameters not in mapping file</li>` : ''}
                            <li>Update mode: ${updateMode === 'replace' ? 'Replaced all fields' :
                                              updateMode === 'add' ? 'Added new fields' :
                                              'Updated existing fields only'}</li>
                            <li>Updated fields: ${fieldsToUpdate.join(', ')}</li>
                            <li>File ready for download</li>
                        `;

                        // Log the first updated item for debugging
                        if (updatedJsonData.summary_items.length > 0 && updatedJsonData.summary_items[0].suggestions) {
                            console.log('First updated item suggestions:', JSON.stringify(updatedJsonData.summary_items[0].suggestions, null, 2));
                        }
                    }, 500);
                }
            });
        }

        // Download updated JSON
        function downloadUpdatedJson() {
            if (!updatedJsonData) {
                showStatus('No updated JSON data to download.', 'error');
                return;
            }

            // Create a deep copy of the data to ensure we don't modify the original
            const finalJsonData = JSON.parse(JSON.stringify(updatedJsonData));

            // Counters for summary
            let blankTestsCleared = 0;
            let unmappedTestsCleared = 0;

            // FINAL CHECK: Ensure all unmapped tests have empty suggestions arrays
            for (let i = 0; i < finalJsonData.summary_items.length; i++) {
                const item = finalJsonData.summary_items[i];
                if (!item.testName) continue;

                const jsonTestName = item.testName;

                // Rule 1: Tests with "Blank" in their name should always have empty suggestions
                if (jsonTestName.includes("Blank")) {
                    console.log(`RULE 1: Clearing suggestions for test with "Blank" in name: ${jsonTestName}`);
                    item.suggestions = [];
                    blankTestsCleared++;
                    continue; // Skip further checks for this item
                }

                // Rule 2: Tests that aren't mapped should have empty suggestions
                const csvTestName = testNameMappings[jsonTestName];
                const isMapped = csvTestName && csvTestData[csvTestName];

                if (!isMapped) {
                    console.log(`RULE 2: Clearing suggestions for unmapped test: ${jsonTestName}`);
                    item.suggestions = [];
                    unmappedTestsCleared++;
                }
                // Rule 3: Only tests with valid mappings should retain their suggestion content
                // (No action needed here as we're only clearing unmapped tests)
            }

            // Log summary of changes
            console.log(`Final check complete: Cleared suggestions for ${blankTestsCleared} tests with "Blank" in name and ${unmappedTestsCleared} unmapped tests`);

            // Log the first few items to verify changes
            console.log("First few items before download:",
                JSON.stringify(finalJsonData.summary_items.slice(0, 3), null, 2));

            const jsonString = JSON.stringify(finalJsonData, null, 2);
            const blob = new Blob([jsonString], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `updated_${jsonFileName}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showStatus(`Updated JSON file downloaded successfully. Cleared suggestions for ${blankTestsCleared} tests with "Blank" in name and ${unmappedTestsCleared} unmapped tests.`, 'success');
        }

        // Convert field ID to title
        function fieldIdToTitle(fieldId) {
            switch (fieldId) {
                case 'about-test':
                    return 'About Test';
                case 'cause-of-abnormal-result':
                    return 'Cause of abnormal result';
                case 'impact':
                    return 'Impact';
                case 'how-to-improve':
                    return 'How to improve?';
                case 'generic-field':
                    return 'Custom Field';
                default:
                    // Handle custom field IDs
                    if (fieldId.startsWith('custom-')) {
                        // Convert hyphenated ID back to readable title
                        return fieldId.substring(7).replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                    }
                    return fieldId;
            }
        }

        // Toggle CSV export options
        function toggleCsvExportOptions() {
            if (csvExportOptions.style.display === 'none') {
                csvExportOptions.style.display = 'block';
                exportCsvButton.classList.add('active');
            } else {
                csvExportOptions.style.display = 'none';
                exportCsvButton.classList.remove('active');
            }
        }

        // Toggle template upload container
        function toggleTemplateUpload() {
            if (useTemplateCheckbox.checked) {
                templateUploadContainer.style.display = 'block';
                downloadCsvButton.disabled = !csvTemplateData;
            } else {
                templateUploadContainer.style.display = 'none';
                downloadCsvButton.disabled = false;
            }
        }

        // Handle CSV template file selection
        function handleCsvTemplateFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            csvTemplateFileName = file.name;
            csvTemplateFileNameDisplay.textContent = `Selected: ${file.name}`;
            csvTemplateFileNameDisplay.classList.add('active');

            Papa.parse(file, {
                header: true,
                complete: function(results) {
                    csvTemplateData = results.data;
                    showStatus(`CSV template loaded successfully with ${results.meta.fields.length} columns.`, 'success');
                    downloadCsvButton.disabled = false;
                },
                error: function(error) {
                    showStatus(`Error parsing CSV template: ${error.message}`, 'error');
                    csvTemplateData = null;
                    downloadCsvButton.disabled = true;
                }
            });
        }

        // Export CSV data
        function exportCsvData() {
            if (!csvData || !jsonData) {
                showStatus('No data available to export.', 'error');
                return;
            }

            // Get selected tests from mapped table
            const selectedTests = [];
            mappedTableBody.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
                selectedTests.push(parseInt(checkbox.dataset.index));
            });

            if (selectedTests.length === 0) {
                showStatus('No parameters selected for export. Please select at least one parameter in the Mapped Parameters tab.', 'error');
                return;
            }

            // Prepare data for export
            let exportData = [];

            // If using template
            if (useTemplateCheckbox.checked && csvTemplateData) {
                // Get template headers
                const templateHeaders = Object.keys(csvTemplateData[0]);

                // Create a mapping between template headers and CSV data
                const headerMapping = {};

                // Try to automatically map headers
                for (const templateHeader of templateHeaders) {
                    // Convert to lowercase for case-insensitive matching
                    const lowerTemplateHeader = templateHeader.toLowerCase();

                    // Try to find a matching column in the CSV data
                    let bestMatch = null;
                    let bestScore = 0;

                    for (const csvColumn of Object.keys(csvData[0])) {
                        const lowerCsvColumn = csvColumn.toLowerCase();

                        // Check for exact match
                        if (lowerTemplateHeader === lowerCsvColumn) {
                            bestMatch = csvColumn;
                            break;
                        }

                        // Check for substring match
                        if (lowerTemplateHeader.includes(lowerCsvColumn) ||
                            lowerCsvColumn.includes(lowerTemplateHeader)) {
                            const similarity = calculateSimilarity(templateHeader, csvColumn);
                            if (similarity > bestScore) {
                                bestScore = similarity;
                                bestMatch = csvColumn;
                            }
                        }
                    }

                    if (bestMatch && bestScore > 0.5) {
                        headerMapping[templateHeader] = bestMatch;
                    } else {
                        headerMapping[templateHeader] = null; // No match found
                    }
                }

                console.log('Header mapping:', headerMapping);

                // Create export data using the template structure
                selectedTests.forEach(index => {
                    const item = jsonData.summary_items[index];
                    const jsonTestName = item.testName;
                    const csvTestName = testNameMappings[jsonTestName];

                    if (csvTestName && csvTestData[csvTestName]) {
                        const csvRow = csvTestData[csvTestName];
                        const exportRow = {};

                        // Map data according to template headers
                        for (const [templateHeader, csvHeader] of Object.entries(headerMapping)) {
                            if (csvHeader && csvRow[csvHeader]) {
                                exportRow[templateHeader] = csvRow[csvHeader];
                            } else {
                                exportRow[templateHeader] = ''; // Empty if no mapping
                            }
                        }

                        exportData.push(exportRow);
                    }
                });
            } else {
                // Use default export format
                selectedTests.forEach(index => {
                    const item = jsonData.summary_items[index];
                    const jsonTestName = item.testName;
                    const csvTestName = testNameMappings[jsonTestName];

                    if (csvTestName && csvTestData[csvTestName]) {
                        const csvRow = csvTestData[csvTestName];

                        exportData.push({
                            'Test Name': jsonTestName,
                            'CSV Parameter': csvTestName,
                            'About Test': csvRow['About Parameter'] || '',
                            'Cause of abnormal result': csvRow['Cause of abnormal result'] || '',
                            'Impact of abnormal result': csvRow['Impact of abnormal result'] || '',
                            'How to improve': csvRow['How to improve'] || ''
                        });
                    }
                });
            }

            // Convert to CSV and download
            const csv = Papa.unparse(exportData);
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = useTemplateCheckbox.checked ?
                `exported_data_with_template.csv` :
                `exported_data.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showStatus(`CSV data exported successfully with ${exportData.length} rows.`, 'success');

            // Close the export options
            toggleCsvExportOptions();
        }

        // Function to clear suggestions for tests with "Blank" in their name and unmapped tests
        function clearBlankTests() {
            if (!jsonData || !jsonData.summary_items) {
                showStatus('No JSON data available. Please upload a JSON file first.', 'error');
                return;
            }

            // Create a deep copy of the JSON data if we don't have updated data yet
            if (!updatedJsonData) {
                updatedJsonData = JSON.parse(JSON.stringify(jsonData));
            }

            // Counters for summary
            let blankTestsCleared = 0;
            let unmappedTestsCleared = 0;

            // Process each test
            updatedJsonData.summary_items.forEach(item => {
                if (!item.testName) return;

                const jsonTestName = item.testName;

                // Rule 1: Tests with "Blank" in their name should always have empty suggestions
                if (jsonTestName.includes("Blank")) {
                    console.log(`RULE 1: Clearing suggestions for test with "Blank" in name: ${jsonTestName}`);
                    item.suggestions = [];
                    blankTestsCleared++;
                    return; // Skip further checks for this item
                }

                // Rule 2: Tests that aren't mapped should have empty suggestions
                const csvTestName = testNameMappings[jsonTestName];
                const isMapped = csvTestName && csvTestData[csvTestName];

                if (!isMapped) {
                    console.log(`RULE 2: Clearing suggestions for unmapped test: ${jsonTestName}`);
                    item.suggestions = [];
                    unmappedTestsCleared++;
                }
                // Rule 3: Only tests with valid mappings should retain their suggestion content
                // (No action needed here as we're only clearing unmapped tests)
            });

            // Show status message
            const totalCleared = blankTestsCleared + unmappedTestsCleared;
            if (totalCleared > 0) {
                showStatus(`Successfully cleared suggestions for ${blankTestsCleared} tests with "Blank" in name and ${unmappedTestsCleared} unmapped tests.`, 'success');

                // Enable download button
                downloadButton.style.display = 'block';
                updateComplete.style.display = 'block';

                // Update summary
                changeSummary.innerHTML = `
                    <li>Cleared suggestions for ${blankTestsCleared} tests with "Blank" in their name</li>
                    <li>Cleared suggestions for ${unmappedTestsCleared} unmapped tests</li>
                    <li>File ready for download</li>
                `;
            } else {
                showStatus('No tests with "Blank" in their name or unmapped tests were found.', 'info');
            }
        }

        // Function to download AI-generated data as Excel
        function downloadAiGeneratedData() {
            if (!aiGenerationComplete || aiGeneratedTests.length === 0) {
                showStatus('No AI-generated data available for download.', 'error');
                return;
            }

            try {
                // Show loading indicator
                const button = document.getElementById('downloadAiDataButton');
                const originalContent = button.innerHTML;
                button.disabled = true;
                button.innerHTML = '<span class="material-icons-round">sync</span> Generating Excel...';

                // Create workbook and worksheet
                const wb = XLSX.utils.book_new();

                // Prepare data for Excel export
                const excelData = aiGeneratedTests.map(test => ({
                    'Test ID': test.testId || '',
                    'Test Name': test.testName || '',
                    'About Test': test.aboutTest || '',
                    'Cause of Abnormal': test.causeAbnormal || '',
                    'Impact of Abnormal': test.impactAbnormal || '',
                    'How to Improve': test.howToImprove || ''
                }));

                // Create worksheet from data
                const ws = XLSX.utils.json_to_sheet(excelData);

                // Set column widths for better readability
                const colWidths = [
                    { wch: 15 }, // Test ID
                    { wch: 25 }, // Test Name
                    { wch: 50 }, // About Test
                    { wch: 50 }, // Cause of Abnormal
                    { wch: 50 }, // Impact of Abnormal
                    { wch: 50 }  // How to Improve
                ];
                ws['!cols'] = colWidths;

                // Add worksheet to workbook
                XLSX.utils.book_append_sheet(wb, ws, 'AI Generated Content');

                // Generate filename with timestamp
                const now = new Date();
                const timestamp = now.toISOString().slice(0, 16).replace('T', '_').replace(/:/g, '-');
                const filename = `AI_Generated_Content_${timestamp}.xlsx`;

                // Write and download the file
                XLSX.writeFile(wb, filename);

                // Show success message
                showStatus(`Successfully exported ${aiGeneratedTests.length} AI-generated test records to ${filename}`, 'success');

                // Reset button
                button.disabled = false;
                button.innerHTML = originalContent;

            } catch (error) {
                console.error('Excel export failed:', error);
                showStatus(`Failed to export Excel file: ${error.message}`, 'error');

                // Reset button
                const button = document.getElementById('downloadAiDataButton');
                button.disabled = false;
                button.innerHTML = '<span class="material-icons-round">table_chart</span> Download AI Generated Data';
            }
        }

        // Function to add AI-generated content to the export data structure
        function addAiGeneratedTest(testId, testName, contentType, generatedContent) {
            // Find existing test or create new one
            let existingTest = aiGeneratedTests.find(test => test.testId === testId);

            if (!existingTest) {
                existingTest = {
                    testId: testId,
                    testName: testName,
                    aboutTest: '',
                    causeAbnormal: '',
                    impactAbnormal: '',
                    howToImprove: ''
                };
                aiGeneratedTests.push(existingTest);
            }

            // Add the generated content to the appropriate field
            switch (contentType) {
                case 'aboutTest':
                    existingTest.aboutTest = generatedContent;
                    break;
                case 'causeAbnormal':
                    existingTest.causeAbnormal = generatedContent;
                    break;
                case 'impactAbnormal':
                    existingTest.impactAbnormal = generatedContent;
                    break;
                case 'howToImprove':
                    existingTest.howToImprove = generatedContent;
                    break;
            }
        }

        // Function to show/hide AI download button based on generation status
        function updateAiDownloadButton() {
            const button = document.getElementById('downloadAiDataButton');
            if (currentContentMode === 'ai' && aiGenerationComplete && aiGeneratedTests.length > 0) {
                button.style.display = 'block';
            } else {
                button.style.display = 'none';
            }
        }

        // Function to replace all Lorem Ipsum content with empty suggestions arrays
        function replaceLoremIpsum() {
            if (!jsonData || !jsonData.summary_items) {
                showStatus('No JSON data available. Please upload a JSON file first.', 'error');
                return;
            }

            // Create a deep copy of the JSON data if we don't have updated data yet
            if (!updatedJsonData) {
                updatedJsonData = JSON.parse(JSON.stringify(jsonData));
            }

            // Counter for summary
            let replacedCount = 0;

            // The default Lorem Ipsum pattern to look for
            const loremIpsumPattern = /Lorem ipsum dolor sit amet, consectetur adipiscing elit/i;

            // Process each test
            updatedJsonData.summary_items.forEach(item => {
                if (!item.suggestions || !Array.isArray(item.suggestions)) return;

                // Check if this item has the default Lorem Ipsum content
                let hasLoremIpsum = false;

                if (item.suggestions.length > 0) {
                    // Check if any suggestion contains Lorem Ipsum
                    for (const suggestion of item.suggestions) {
                        if (suggestion.content && loremIpsumPattern.test(suggestion.content)) {
                            hasLoremIpsum = true;
                            break;
                        }
                    }

                    // If this item has Lorem Ipsum content, replace it with an empty array
                    if (hasLoremIpsum) {
                        console.log(`Replacing Lorem Ipsum content for test: ${item.testName}`);
                        item.suggestions = [];
                        replacedCount++;
                    }
                }
            });

            // Show status message
            if (replacedCount > 0) {
                showStatus(`Successfully replaced Lorem Ipsum content with empty suggestions for ${replacedCount} tests.`, 'success');

                // Enable download button
                downloadButton.style.display = 'block';
                updateComplete.style.display = 'block';

                // Update summary
                changeSummary.innerHTML = `
                    <li>Replaced Lorem Ipsum content with empty suggestions for ${replacedCount} tests</li>
                    <li>File ready for download</li>
                `;
            } else {
                showStatus('No tests with Lorem Ipsum content were found.', 'info');
            }
        }

        // Show status message
        function showStatus(message, type = 'info', isHTML = false) {
            const iconMap = {
                'success': 'check_circle',
                'error': 'error',
                'info': 'info',
                'warning': 'warning'
            };

            const icon = iconMap[type] || 'info';

            // If message is HTML, insert it directly, otherwise escape it
            const messageContent = isHTML ? message : `<div>${message}</div>`;

            statusMessage.innerHTML = `
                <div class="status status-${type}">
                    <span class="material-icons-round">${icon}</span>
                    ${messageContent}
                </div>
            `;

            // For detailed HTML messages (like mapping results), don't auto-clear
            if (!isHTML) {
                // Clear after 5 seconds
                setTimeout(() => {
                    statusMessage.innerHTML = '';
                }, 5000);
            }
        }
    </script>
</body>
</html>
